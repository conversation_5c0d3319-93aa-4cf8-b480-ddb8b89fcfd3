# Comprehensive Migration Plan: wp-ai-app-hisham → wp-ai-app

## Overview
This document outlines the complete migration plan to transfer the authentication system, payment page, and dashboard page from the 'wp-ai-app-hisham' repository to the 'wp-ai-app' repository.

## Current State Analysis

### Source Repository (wp-ai-app-hisham)
- **Technology Stack**: Next.js 14.1.0, React 18.2.0, TypeScript
- **Authentication**: Supabase with custom AuthProvider
- **Payments**: Stripe integration with subscription and domain payments
- **Database**: Supabase with profiles, domains, and user-websites tables
- **UI Framework**: Tailwind CSS, Radix UI, Material-UI components

### Target Repository (wp-ai-app)
- **Technology Stack**: Next.js 14.1.0, React 18.2.0, TypeScript
- **Current Features**: Site builder, template system, content generation
- **Missing**: Authentication, payment processing, user dashboard
- **Package Manager**: pnpm (vs npm in source)

## Migration Phases

### Phase 1: Pre-Migration Analysis and Setup ✅
**Estimated Time**: 2-3 hours

#### 1.1 Analyze source repository structure and components
- **Authentication Components**:
  - `src/components/providers/AuthProvider.tsx` - Main auth context
  - `src/app/login/page.tsx` - Login page with OAuth support
  - `src/app/signup/page.tsx` - Registration page
  - `src/lib/wordpress-auth.ts` - WordPress API authentication

- **Payment Components**:
  - `src/app/payments/page.tsx` - Main payment page with plans
  - `src/components/StripeModal.tsx` - Payment modal component
  - `src/app/EmbeddedCheckoutForm.tsx` - Embedded checkout
  - `src/lib/stripe.ts` - Stripe configuration
  - `src/lib/stripe-plans.ts` - Plan definitions
  - `src/lib/stripe-customer.ts` - Customer management

- **Dashboard Components**:
  - `src/app/dashboard/layout.tsx` - Dashboard layout with sidebar
  - `src/app/dashboard/page.tsx` - Main dashboard (websites)
  - `src/app/dashboard/account/page.tsx` - User profile management
  - `src/app/dashboard/billing/page.tsx` - Billing management
  - `src/app/dashboard/domain/` - Domain management pages

#### 1.2 Analyze target repository structure
- **Existing Components**: Site builder, template system, API routes
- **Potential Conflicts**: None identified - clean migration path
- **Integration Points**: Will need to integrate with existing layout and routing

#### 1.3 Create migration checklist and file mapping
- **Total Files to Migrate**: ~45 files
- **New Dependencies**: 15+ packages
- **API Endpoints**: 12+ new routes

#### 1.4 Backup target repository
- Create git branch: `backup-pre-migration`
- Document current state

### Phase 2: Dependency and Package Management ⏳
**Estimated Time**: 1-2 hours

#### 2.1 Install Supabase dependencies
```bash
pnpm add @supabase/supabase-js @supabase/auth-helpers-nextjs
```

#### 2.2 Install Stripe dependencies
```bash
pnpm add @stripe/stripe-js @stripe/react-stripe-js stripe
```

#### 2.3 Install UI and utility dependencies
```bash
pnpm add @radix-ui/react-tooltip @mui/material @mui/icons-material uuid
pnpm add @emotion/react @emotion/styled react-tooltip remark-gfm
```

#### 2.4 Update TypeScript types
```bash
pnpm add -D @types/uuid
```

### Phase 3: Authentication System Migration ⏳
**Estimated Time**: 4-6 hours

#### 3.1 Migrate AuthProvider component
- **Source**: `src/components/providers/AuthProvider.tsx`
- **Target**: `src/components/providers/AuthProvider.tsx`
- **Features**: Supabase integration, profile management, session handling

#### 3.2 Migrate login page
- **Source**: `src/app/login/page.tsx`
- **Target**: `src/app/login/page.tsx`
- **Features**: Email/password login, OAuth (Google, Apple, Azure)

#### 3.3 Migrate signup page
- **Source**: `src/app/signup/page.tsx`
- **Target**: `src/app/signup/page.tsx`
- **Features**: User registration, terms acceptance

#### 3.4 Migrate authentication API endpoints
- **Routes to migrate**:
  - `/api/auth/*` - Authentication endpoints
  - `/api/stripe/customer/*` - Customer management

#### 3.5 Migrate WordPress authentication utilities
- **Source**: `src/lib/wordpress-auth.ts`
- **Target**: `src/lib/wordpress-auth.ts`

#### 3.6 Update root layout with AuthProvider
- Wrap app with AuthProvider in `src/app/layout.tsx`

### Phase 4: Payment System Migration ⏳
**Estimated Time**: 6-8 hours

#### 4.1 Migrate Stripe configuration and utilities
- **Files**:
  - `src/lib/stripe.ts` - Main Stripe config
  - `src/lib/stripe-plans.ts` - Plan definitions
  - `src/lib/stripe-customer.ts` - Customer management

#### 4.2 Migrate payment pages
- **Source**: `src/app/payments/page.tsx`
- **Target**: `src/app/payments/page.tsx`
- **Features**: Plan selection, yearly/monthly toggle, checkout

#### 4.3 Migrate Stripe components
- **Files**:
  - `src/components/StripeModal.tsx`
  - `src/app/EmbeddedCheckoutForm.tsx`

#### 4.4 Migrate payment API endpoints
- **Routes**:
  - `/api/create-checkout-session`
  - `/api/create-payment-intent`
  - `/api/verify-session`
  - `/api/stripe/billing-data`

#### 4.5 Migrate domain payment functionality
- **Routes**: `/api/domain-checkout-session`
- **Components**: Domain payment forms

#### 4.6 Update existing pricing components
- Integrate with existing `src/components/landing/Pricing.tsx`

### Phase 5: Dashboard System Migration ⏳
**Estimated Time**: 8-10 hours

#### 5.1 Migrate dashboard layout
- **Source**: `src/app/dashboard/layout.tsx`
- **Features**: Sidebar navigation, responsive design, user menu

#### 5.2 Migrate main dashboard page
- **Source**: `src/app/dashboard/page.tsx`
- **Features**: Website listing, search, site management

#### 5.3 Migrate account/profile page
- **Source**: `src/app/dashboard/account/page.tsx`
- **Features**: Profile editing, user settings

#### 5.4 Migrate billing page
- **Source**: `src/app/dashboard/billing/page.tsx`
- **Features**: Subscription management, billing history

#### 5.5 Migrate domain management pages
- **Source**: `src/app/dashboard/domain/`
- **Features**: Domain registration, management, DNS configuration

#### 5.6 Migrate dashboard components and utilities
- Modal components, utility functions, hooks

### Phase 6: Configuration and Environment Setup ⏳
**Estimated Time**: 2-3 hours

#### 6.1 Create environment configuration file
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ermaaxnoyckezbjtegmq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RgxfeHKmibvltr1...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RgxfeHKmibvltr1...

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Domain Registration (Namecheap)
NAMECHEAP_API_USER=omarkk
NAMECHEAP_API_KEY=ec4a0a2c1bc54454959a8a92e0fa4ff5
# ... other Namecheap config
```

#### 6.2 Migrate database schema and migrations
- **Files**: `migrations/create_domains_table.sql`
- **Tables**: profiles, domains, user-websites

#### 6.3 Update Next.js configuration
- Review and update `next.config.js` if needed

#### 6.4 Configure Supabase types
- **Source**: `supabase-types.ts`
- **Target**: `src/types/supabase.ts`

## Critical Considerations

### 1. Database Schema Requirements
The migration requires these Supabase tables:
- `profiles` - User profile information
- `domains` - Domain registration tracking
- `user-websites` - Website management

### 2. Environment Variables
All environment variables from source must be configured in target.

### 3. Package Manager Compatibility
Source uses npm, target uses pnpm - ensure compatibility.

### 4. Routing Integration
Dashboard routes must integrate with existing app routing structure.

### 5. Authentication Flow
Ensure authentication works with existing site builder flow.

## Risk Assessment

### High Risk
- Database schema conflicts
- Environment variable misconfigurations
- Stripe webhook configurations

### Medium Risk
- Component naming conflicts
- Routing conflicts with existing pages
- Package version incompatibilities

### Low Risk
- UI styling conflicts (both use Tailwind)
- TypeScript type conflicts

## Success Criteria

### Authentication System
- ✅ Users can register and login
- ✅ OAuth providers work correctly
- ✅ Session management functions properly
- ✅ Profile management works

### Payment System
- ✅ Stripe checkout process works
- ✅ Subscription management functions
- ✅ Domain payments process correctly
- ✅ Billing history displays

### Dashboard System
- ✅ All dashboard pages load correctly
- ✅ Navigation works properly
- ✅ User can manage websites
- ✅ Domain management functions
- ✅ Account settings work

### Integration
- ✅ Migrated components work with existing site builder
- ✅ No conflicts with existing functionality
- ✅ Performance is maintained
- ✅ All tests pass

## Next Steps

1. **Start with Phase 1**: Complete analysis and setup
2. **Proceed sequentially**: Follow phases in order
3. **Test incrementally**: Test each phase before proceeding
4. **Document issues**: Track any problems encountered
5. **Verify integration**: Ensure compatibility with existing features

## Estimated Total Time: 25-35 hours

This migration plan provides a comprehensive roadmap for successfully transferring all authentication, payment, and dashboard functionality while maintaining the integrity of the existing wp-ai-app codebase.

-- Create domains table for comprehensive domain management
-- This migration creates a new table to track user domains with proper relationships

-- Create domains table
CREATE TABLE IF NOT EXISTS domains (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  domain_name TEXT NOT NULL UNIQUE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  site_id UUID REFERENCES "user-websites"(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'registered', 'active', 'expired', 'failed')),
  registration_date TIMESTAMP,
  expiry_date TIMESTAMP,
  auto_renew BOOLEAN DEFAULT true,
  price_paid DECIMAL(10,2),
  currency TEXT DEFAULT 'AUD',
  namecheap_order_id TEXT,
  namecheap_transaction_id TEXT,
  stripe_session_id TEXT,
  dns_configured BOOLEAN DEFAULT false,
  cname_target TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_domains_user_id ON domains (user_id);
CREATE INDEX IF NOT EXISTS idx_domains_site_id ON domains (site_id);
CREATE INDEX IF NOT EXISTS idx_domains_status ON domains (status);
CREATE INDEX IF NOT EXISTS idx_domains_domain_name ON domains (domain_name);
CREATE INDEX IF NOT EXISTS idx_domains_expiry_date ON domains (expiry_date);

-- Add comments to document the table and columns
COMMENT ON TABLE domains IS 'Stores user domain registrations and their associated metadata';
COMMENT ON COLUMN domains.domain_name IS 'The registered domain name (e.g., example.com)';
COMMENT ON COLUMN domains.user_id IS 'Reference to the user who owns this domain';
COMMENT ON COLUMN domains.site_id IS 'Reference to the site this domain is mapped to (nullable)';
COMMENT ON COLUMN domains.status IS 'Current status of the domain registration';
COMMENT ON COLUMN domains.registration_date IS 'When the domain was successfully registered';
COMMENT ON COLUMN domains.expiry_date IS 'When the domain registration expires';
COMMENT ON COLUMN domains.auto_renew IS 'Whether the domain should auto-renew before expiry';
COMMENT ON COLUMN domains.price_paid IS 'Amount paid for domain registration';
COMMENT ON COLUMN domains.currency IS 'Currency used for payment';
COMMENT ON COLUMN domains.namecheap_order_id IS 'Namecheap order ID for tracking';
COMMENT ON COLUMN domains.namecheap_transaction_id IS 'Namecheap transaction ID for tracking';
COMMENT ON COLUMN domains.stripe_session_id IS 'Stripe checkout session ID for payment tracking';
COMMENT ON COLUMN domains.dns_configured IS 'Whether DNS/CNAME has been properly configured';
COMMENT ON COLUMN domains.cname_target IS 'The CNAME target this domain points to';

-- Create a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_domains_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_domains_updated_at
  BEFORE UPDATE ON domains
  FOR EACH ROW
  EXECUTE FUNCTION update_domains_updated_at();

-- Add RLS (Row Level Security) policies for domains table
ALTER TABLE domains ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own domains
CREATE POLICY "Users can view their own domains" ON domains
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can only insert domains for themselves
CREATE POLICY "Users can insert their own domains" ON domains
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only update their own domains
CREATE POLICY "Users can update their own domains" ON domains
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can only delete their own domains
CREATE POLICY "Users can delete their own domains" ON domains
  FOR DELETE USING (auth.uid() = user_id);

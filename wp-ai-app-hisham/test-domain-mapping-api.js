// Test script for domain mapping API
// Run with: node test-domain-mapping-api.js

const BASE_URL = 'http://localhost:3000';

// Test domain mapping API
async function testDomainMappingAPI() {
  console.log('🔗 Testing domain mapping API...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/domain-mapping`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        // Note: This will fail without proper auth token
        'Authorization': 'Bearer fake_token'
      },
      body: JSON.stringify({
        domainName: 'test-domain.com',
        siteId: 'fake-site-id'
      }),
    });

    const data = await response.json();
    
    if (response.status === 401 && data.error === 'Not authenticated') {
      console.log('✅ Domain mapping API working (expected auth error)');
      console.log('   API correctly requires authentication');
    } else if (response.ok) {
      console.log('✅ Domain mapping API working');
      console.log(`   Mapping result: ${data.success ? 'Success' : 'Failed'}`);
    } else {
      console.log('❌ Domain mapping API failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Domain mapping API error:', error.message);
  }
}

// Test with missing parameters
async function testDomainMappingValidation() {
  console.log('\n📝 Testing domain mapping validation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/domain-mapping`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake_token'
      },
      body: JSON.stringify({
        // Missing domainName and siteId
      }),
    });

    const data = await response.json();
    
    if (response.status === 400 && data.error.includes('required')) {
      console.log('✅ Domain mapping validation working');
      console.log('   API correctly validates required parameters');
    } else if (response.status === 401) {
      console.log('✅ Domain mapping validation working (auth check first)');
    } else {
      console.log('❌ Domain mapping validation failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Domain mapping validation error:', error.message);
  }
}

// Test API endpoint structure
async function testAPIStructure() {
  console.log('\n🏗️ Testing API structure...');
  
  const endpoints = [
    '/api/domains',
    '/api/domain-mapping',
    '/api/domain-checkout-session',
    '/api/namecheap'
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.status === 405) {
        console.log(`✅ ${endpoint} - Method not allowed (expected for POST-only endpoints)`);
      } else if (response.status === 401) {
        console.log(`✅ ${endpoint} - Authentication required (expected)`);
      } else if (response.status === 400) {
        console.log(`✅ ${endpoint} - Bad request (expected for missing params)`);
      } else {
        console.log(`ℹ️  ${endpoint} - Status: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Error: ${error.message}`);
    }
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Starting Domain Mapping API Tests\n');
  console.log('Note: Tests expect authentication errors for security validation\n');
  
  await testDomainMappingAPI();
  await testDomainMappingValidation();
  await testAPIStructure();
  
  console.log('\n✨ Test suite completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Test the complete domain registration wizard in the browser');
  console.log('2. Verify site mapping functionality with real user accounts');
  console.log('3. Test CNAME configuration with Namecheap sandbox');
  console.log('4. Validate database updates after mapping');
}

// Check if we're running in Node.js environment
if (typeof window === 'undefined') {
  // Node.js environment - need to install node-fetch
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    runTests();
  } catch (error) {
    console.log('❌ Please install node-fetch: npm install node-fetch');
    console.log('Or run this script in a browser console instead.');
  }
} else {
  // Browser environment
  runTests();
}

[{"/home/<USER>/Desktop/wp-ai-app/src/app/EmbeddedCheckoutForm.tsx": "1", "/home/<USER>/Desktop/wp-ai-app/src/app/account/page.tsx": "2", "/home/<USER>/Desktop/wp-ai-app/src/app/api/ai-generate-homepage/route.ts": "3", "/home/<USER>/Desktop/wp-ai-app/src/app/api/ai-update-content/route.ts": "4", "/home/<USER>/Desktop/wp-ai-app/src/app/api/chat/route.ts": "5", "/home/<USER>/Desktop/wp-ai-app/src/app/api/check-task-status/route.ts": "6", "/home/<USER>/Desktop/wp-ai-app/src/app/api/create-checkout-session/route.ts": "7", "/home/<USER>/Desktop/wp-ai-app/src/app/api/create-embedded-checkout/route.ts": "8", "/home/<USER>/Desktop/wp-ai-app/src/app/api/create-payment-intent/route.ts": "9", "/home/<USER>/Desktop/wp-ai-app/src/app/api/customize-site/route.ts": "10", "/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-checkout-session/route.ts": "11", "/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-mapping/route.ts": "12", "/home/<USER>/Desktop/wp-ai-app/src/app/api/domains/route.ts": "13", "/home/<USER>/Desktop/wp-ai-app/src/app/api/embedded-checkout/route.ts": "14", "/home/<USER>/Desktop/wp-ai-app/src/app/api/extract-keywords/route.ts": "15", "/home/<USER>/Desktop/wp-ai-app/src/app/api/generate-content/route.ts": "16", "/home/<USER>/Desktop/wp-ai-app/src/app/api/generate-image/route.ts": "17", "/home/<USER>/Desktop/wp-ai-app/src/app/api/images/route.ts": "18", "/home/<USER>/Desktop/wp-ai-app/src/app/api/namecheap/route.ts": "19", "/home/<USER>/Desktop/wp-ai-app/src/app/api/registrant-info/route.ts": "20", "/home/<USER>/Desktop/wp-ai-app/src/app/api/stripe/billing-data/route.ts": "21", "/home/<USER>/Desktop/wp-ai-app/src/app/api/stripe/customer/route.ts": "22", "/home/<USER>/Desktop/wp-ai-app/src/app/api/theme-keywords/route.ts": "23", "/home/<USER>/Desktop/wp-ai-app/src/app/api/verify-session/route.ts": "24", "/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-content/route.ts": "25", "/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-theme/[themeId]/route.ts": "26", "/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-themes/route.ts": "27", "/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx": "28", "/home/<USER>/Desktop/wp-ai-app/src/app/assistant.tsx": "29", "/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx": "30", "/home/<USER>/Desktop/wp-ai-app/src/app/components/SiteCustomizer.tsx": "31", "/home/<USER>/Desktop/wp-ai-app/src/app/content-editor/page.tsx": "32", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx": "33", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx": "34", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx": "35", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx": "36", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx": "37", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx": "38", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx": "39", "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx": "40", "/home/<USER>/Desktop/wp-ai-app/src/app/domain-setup/page.tsx": "41", "/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx": "42", "/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx": "43", "/home/<USER>/Desktop/wp-ai-app/src/app/page.tsx": "44", "/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx": "45", "/home/<USER>/Desktop/wp-ai-app/src/app/return/page.tsx": "46", "/home/<USER>/Desktop/wp-ai-app/src/app/signup/page.tsx": "47", "/home/<USER>/Desktop/wp-ai-app/src/app/success/page.tsx": "48", "/home/<USER>/Desktop/wp-ai-app/src/app/test-task-status/page.tsx": "49", "/home/<USER>/Desktop/wp-ai-app/src/app/test-url-fix/page.tsx": "50", "/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx": "51", "/home/<USER>/Desktop/wp-ai-app/src/app/themes/[themeId]/page.tsx": "52", "/home/<USER>/Desktop/wp-ai-app/src/app/themes/page.tsx": "53", "/home/<USER>/Desktop/wp-ai-app/src/app/themes/preview/[slug]/page.tsx": "54", "/home/<USER>/Desktop/wp-ai-app/src/components/CustomizationErrorBoundary.tsx": "55", "/home/<USER>/Desktop/wp-ai-app/src/components/EmbeddedCheckoutForm.tsx": "56", "/home/<USER>/Desktop/wp-ai-app/src/components/RegistrantInfoModal.tsx": "57", "/home/<USER>/Desktop/wp-ai-app/src/components/StripeModal.tsx": "58", "/home/<USER>/Desktop/wp-ai-app/src/components/ThemeDetail.tsx": "59", "/home/<USER>/Desktop/wp-ai-app/src/components/ThemePreview.tsx": "60", "/home/<USER>/Desktop/wp-ai-app/src/components/ThemesGrid.tsx": "61", "/home/<USER>/Desktop/wp-ai-app/src/components/WebsiteWizard.tsx": "62", "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx": "63", "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx": "64", "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread-list.tsx": "65", "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx": "66", "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx": "67", "/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainPaymentStep.tsx": "68", "/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSearchStep.tsx": "69", "/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx": "70", "/home/<USER>/Desktop/wp-ai-app/src/components/domain/MyDomainsSection.tsx": "71", "/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx": "72", "/home/<USER>/Desktop/wp-ai-app/src/components/ui/button.tsx": "73", "/home/<USER>/Desktop/wp-ai-app/src/components/ui/input.tsx": "74", "/home/<USER>/Desktop/wp-ai-app/src/components/ui/textarea.tsx": "75", "/home/<USER>/Desktop/wp-ai-app/src/components/ui/tooltip.tsx": "76", "/home/<USER>/Desktop/wp-ai-app/src/hooks/useCustomization.js": "77", "/home/<USER>/Desktop/wp-ai-app/src/hooks/useDomainSearch.ts": "78", "/home/<USER>/Desktop/wp-ai-app/src/lib/pexels-api.ts": "79", "/home/<USER>/Desktop/wp-ai-app/src/lib/registrant-utils.ts": "80", "/home/<USER>/Desktop/wp-ai-app/src/lib/stripe-customer.ts": "81", "/home/<USER>/Desktop/wp-ai-app/src/lib/stripe-plans.ts": "82", "/home/<USER>/Desktop/wp-ai-app/src/lib/stripe.ts": "83", "/home/<USER>/Desktop/wp-ai-app/src/lib/task-status-checker.ts": "84", "/home/<USER>/Desktop/wp-ai-app/src/lib/utils.ts": "85", "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress-api.js": "86", "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress-auth.ts": "87", "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress-content.ts": "88", "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress.ts": "89"}, {"size": 1976, "mtime": 1751967581321, "results": "90", "hashOfConfig": "91"}, {"size": 2855, "mtime": 1753169594584, "results": "92", "hashOfConfig": "91"}, {"size": 1265, "mtime": 1751356843937, "results": "93", "hashOfConfig": "91"}, {"size": 2139, "mtime": 1750932392387, "results": "94", "hashOfConfig": "91"}, {"size": 1343, "mtime": 1747336272117, "results": "95", "hashOfConfig": "91"}, {"size": 1647, "mtime": 1750828649116, "results": "96", "hashOfConfig": "91"}, {"size": 2380, "mtime": 1753244291870, "results": "97", "hashOfConfig": "91"}, {"size": 1728, "mtime": 1752037089250, "results": "98", "hashOfConfig": "91"}, {"size": 2494, "mtime": 1751966402472, "results": "99", "hashOfConfig": "91"}, {"size": 15822, "mtime": 1751446394807, "results": "100", "hashOfConfig": "91"}, {"size": 2391, "mtime": 1753248973982, "results": "101", "hashOfConfig": "91"}, {"size": 6253, "mtime": 1753253101861, "results": "102", "hashOfConfig": "91"}, {"size": 6075, "mtime": 1753246799265, "results": "103", "hashOfConfig": "91"}, {"size": 828, "mtime": 1751967117119, "results": "104", "hashOfConfig": "91"}, {"size": 1896, "mtime": 1747729700454, "results": "105", "hashOfConfig": "91"}, {"size": 965, "mtime": 1747116894388, "results": "106", "hashOfConfig": "91"}, {"size": 835, "mtime": 1747031034033, "results": "107", "hashOfConfig": "91"}, {"size": 1337, "mtime": 1749022894435, "results": "108", "hashOfConfig": "91"}, {"size": 18838, "mtime": 1753254720895, "results": "109", "hashOfConfig": "91"}, {"size": 3351, "mtime": 1753254744612, "results": "110", "hashOfConfig": "91"}, {"size": 3522, "mtime": 1753245871924, "results": "111", "hashOfConfig": "91"}, {"size": 2681, "mtime": 1753244334543, "results": "112", "hashOfConfig": "91"}, {"size": 0, "mtime": 1748855273665, "results": "113", "hashOfConfig": "91"}, {"size": 1866, "mtime": 1753251019963, "results": "114", "hashOfConfig": "91"}, {"size": 6877, "mtime": 1750388548961, "results": "115", "hashOfConfig": "91"}, {"size": 4100, "mtime": 1747982036078, "results": "116", "hashOfConfig": "91"}, {"size": 4782, "mtime": 1747978604574, "results": "117", "hashOfConfig": "91"}, {"size": 242, "mtime": 1747123345207, "results": "118", "hashOfConfig": "91"}, {"size": 558, "mtime": 1747124192710, "results": "119", "hashOfConfig": "91"}, {"size": 753, "mtime": 1751526822535, "results": "120", "hashOfConfig": "91"}, {"size": 9792, "mtime": 1751356823611, "results": "121", "hashOfConfig": "91"}, {"size": 15050, "mtime": 1750388331004, "results": "122", "hashOfConfig": "91"}, {"size": 1200, "mtime": 1752555291454, "results": "123", "hashOfConfig": "91"}, {"size": 4245, "mtime": 1751618360438, "results": "124", "hashOfConfig": "91"}, {"size": 23713, "mtime": 1753251019161, "results": "125", "hashOfConfig": "91"}, {"size": 9332, "mtime": 1753244774521, "results": "126", "hashOfConfig": "91"}, {"size": 9959, "mtime": 1753254835788, "results": "127", "hashOfConfig": "91"}, {"size": 5463, "mtime": 1753247311298, "results": "128", "hashOfConfig": "91"}, {"size": 11353, "mtime": 1753169614808, "results": "129", "hashOfConfig": "91"}, {"size": 16839, "mtime": 1753161929066, "results": "130", "hashOfConfig": "91"}, {"size": 930, "mtime": 1752048869584, "results": "131", "hashOfConfig": "91"}, {"size": 454, "mtime": 1752743290416, "results": "132", "hashOfConfig": "91"}, {"size": 7380, "mtime": 1752740117348, "results": "133", "hashOfConfig": "91"}, {"size": 270, "mtime": 1750754348918, "results": "134", "hashOfConfig": "91"}, {"size": 11255, "mtime": 1753244371194, "results": "135", "hashOfConfig": "91"}, {"size": 701, "mtime": 1751967153762, "results": "136", "hashOfConfig": "91"}, {"size": 5310, "mtime": 1752740118542, "results": "137", "hashOfConfig": "91"}, {"size": 3850, "mtime": 1752554226406, "results": "138", "hashOfConfig": "91"}, {"size": 7851, "mtime": 1750828649116, "results": "139", "hashOfConfig": "91"}, {"size": 7567, "mtime": 1750828649110, "results": "140", "hashOfConfig": "91"}, {"size": 17878, "mtime": 1748854958234, "results": "141", "hashOfConfig": "91"}, {"size": 2636, "mtime": 1747982305736, "results": "142", "hashOfConfig": "91"}, {"size": 2019, "mtime": 1748855273704, "results": "143", "hashOfConfig": "91"}, {"size": 17460, "mtime": 1747982907594, "results": "144", "hashOfConfig": "91"}, {"size": 3021, "mtime": 1748586387212, "results": "145", "hashOfConfig": "91"}, {"size": 1616, "mtime": 1752037089265, "results": "146", "hashOfConfig": "91"}, {"size": 8009, "mtime": 1753254514874, "results": "147", "hashOfConfig": "91"}, {"size": 5517, "mtime": 1751966402469, "results": "148", "hashOfConfig": "91"}, {"size": 7053, "mtime": 1747982924403, "results": "149", "hashOfConfig": "91"}, {"size": 14434, "mtime": 1747982762941, "results": "150", "hashOfConfig": "91"}, {"size": 0, "mtime": 1748855273674, "results": "151", "hashOfConfig": "91"}, {"size": 8132, "mtime": 1747115850175, "results": "152", "hashOfConfig": "91"}, {"size": 6402, "mtime": 1747336563685, "results": "153", "hashOfConfig": "91"}, {"size": 4920, "mtime": 1747120335139, "results": "154", "hashOfConfig": "91"}, {"size": 2005, "mtime": 1747120335179, "results": "155", "hashOfConfig": "91"}, {"size": 21831, "mtime": 1748855273698, "results": "156", "hashOfConfig": "91"}, {"size": 1041, "mtime": 1747120335148, "results": "157", "hashOfConfig": "91"}, {"size": 7909, "mtime": 1753248200910, "results": "158", "hashOfConfig": "91"}, {"size": 7577, "mtime": 1753246866775, "results": "159", "hashOfConfig": "91"}, {"size": 8975, "mtime": 1753246950381, "results": "160", "hashOfConfig": "91"}, {"size": 10347, "mtime": 1753249479730, "results": "161", "hashOfConfig": "91"}, {"size": 3979, "mtime": 1753254447856, "results": "162", "hashOfConfig": "91"}, {"size": 1902, "mtime": 1747031034033, "results": "163", "hashOfConfig": "91"}, {"size": 768, "mtime": 1747031034033, "results": "164", "hashOfConfig": "91"}, {"size": 649, "mtime": 1747031034033, "results": "165", "hashOfConfig": "91"}, {"size": 1267, "mtime": 1747120335162, "results": "166", "hashOfConfig": "91"}, {"size": 8331, "mtime": 1748586152907, "results": "167", "hashOfConfig": "91"}, {"size": 3971, "mtime": 1753246826926, "results": "168", "hashOfConfig": "91"}, {"size": 3713, "mtime": 1750481195731, "results": "169", "hashOfConfig": "91"}, {"size": 2730, "mtime": 1753254474156, "results": "170", "hashOfConfig": "91"}, {"size": 5222, "mtime": 1753244262662, "results": "171", "hashOfConfig": "91"}, {"size": 579, "mtime": 1752563550355, "results": "172", "hashOfConfig": "91"}, {"size": 246, "mtime": 1751967227390, "results": "173", "hashOfConfig": "91"}, {"size": 4520, "mtime": 1750828649111, "results": "174", "hashOfConfig": "91"}, {"size": 166, "mtime": 1747031034034, "results": "175", "hashOfConfig": "91"}, {"size": 9675, "mtime": 1749016980232, "results": "176", "hashOfConfig": "91"}, {"size": 2097, "mtime": 1750388595545, "results": "177", "hashOfConfig": "91"}, {"size": 7041, "mtime": 1750388613915, "results": "178", "hashOfConfig": "91"}, {"size": 3144, "mtime": 1747982984702, "results": "179", "hashOfConfig": "91"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "g7ihcr", {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Desktop/wp-ai-app/src/app/EmbeddedCheckoutForm.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/account/page.tsx", ["447"], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/ai-generate-homepage/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/ai-update-content/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/chat/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/check-task-status/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/create-checkout-session/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/create-embedded-checkout/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/create-payment-intent/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/customize-site/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-checkout-session/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-mapping/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/domains/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/embedded-checkout/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/extract-keywords/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/generate-content/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/generate-image/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/images/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/namecheap/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/registrant-info/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/stripe/billing-data/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/stripe/customer/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/theme-keywords/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/verify-session/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-content/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-theme/[themeId]/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-themes/route.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/assistant.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/components/SiteCustomizer.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/content-editor/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx", ["448", "449"], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx", ["450"], [], "/home/<USER>/Desktop/wp-ai-app/src/app/domain-setup/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/return/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/signup/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/success/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/test-task-status/page.tsx", ["451", "452", "453", "454"], [], "/home/<USER>/Desktop/wp-ai-app/src/app/test-url-fix/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/themes/[themeId]/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/themes/page.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/app/themes/preview/[slug]/page.tsx", ["455"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/CustomizationErrorBoundary.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/EmbeddedCheckoutForm.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/RegistrantInfoModal.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/StripeModal.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ThemeDetail.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ThemePreview.tsx", ["456"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ThemesGrid.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/WebsiteWizard.tsx", ["457"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread-list.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainPaymentStep.tsx", ["458"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSearchStep.tsx", ["459", "460"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx", ["461", "462"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/domain/MyDomainsSection.tsx", ["463", "464"], [], "/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ui/button.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ui/input.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ui/textarea.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/components/ui/tooltip.tsx", [], [], "/home/<USER>/Desktop/wp-ai-app/src/hooks/useCustomization.js", [], [], "/home/<USER>/Desktop/wp-ai-app/src/hooks/useDomainSearch.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/pexels-api.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/registrant-utils.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/stripe-customer.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/stripe-plans.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/stripe.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/task-status-checker.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/utils.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress-api.js", ["465"], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress-auth.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress-content.ts", [], [], "/home/<USER>/Desktop/wp-ai-app/src/lib/wordpress.ts", [], [], {"ruleId": "466", "severity": 1, "message": "467", "line": 21, "column": 9, "nodeType": "468", "endLine": 25, "endColumn": 11}, {"ruleId": "469", "severity": 2, "message": "470", "line": 67, "column": 66, "nodeType": "471", "messageId": "472", "suggestions": "473"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 67, "column": 72, "nodeType": "471", "messageId": "472", "suggestions": "474"}, {"ruleId": "475", "severity": 1, "message": "476", "line": 101, "column": 6, "nodeType": "477", "endLine": 101, "endColumn": 8, "suggestions": "478"}, {"ruleId": "469", "severity": 2, "message": "479", "line": 105, "column": 24, "nodeType": "471", "messageId": "472", "suggestions": "480"}, {"ruleId": "469", "severity": 2, "message": "479", "line": 105, "column": 37, "nodeType": "471", "messageId": "472", "suggestions": "481"}, {"ruleId": "469", "severity": 2, "message": "479", "line": 106, "column": 65, "nodeType": "471", "messageId": "472", "suggestions": "482"}, {"ruleId": "469", "severity": 2, "message": "479", "line": 106, "column": 75, "nodeType": "471", "messageId": "472", "suggestions": "483"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 120, "column": 112, "nodeType": "471", "messageId": "472", "suggestions": "484"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 89, "column": 112, "nodeType": "471", "messageId": "472", "suggestions": "485"}, {"ruleId": "466", "severity": 1, "message": "467", "line": 180, "column": 21, "nodeType": "468", "endLine": 184, "endColumn": 23}, {"ruleId": "469", "severity": 2, "message": "470", "line": 185, "column": 35, "nodeType": "471", "messageId": "472", "suggestions": "486"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 68, "column": 59, "nodeType": "471", "messageId": "472", "suggestions": "487"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 100, "column": 57, "nodeType": "471", "messageId": "472", "suggestions": "488"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 112, "column": 32, "nodeType": "471", "messageId": "472", "suggestions": "489"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 123, "column": 45, "nodeType": "471", "messageId": "472", "suggestions": "490"}, {"ruleId": "475", "severity": 1, "message": "491", "line": 37, "column": 6, "nodeType": "477", "endLine": 37, "endColumn": 12, "suggestions": "492"}, {"ruleId": "469", "severity": 2, "message": "470", "line": 169, "column": 22, "nodeType": "471", "messageId": "472", "suggestions": "493"}, {"ruleId": "494", "severity": 1, "message": "495", "line": 332, "column": 1, "nodeType": "496", "endLine": 341, "endColumn": 3}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["497", "498", "499", "500"], ["501", "502", "503", "504"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'supabase'. Either include it or remove the dependency array.", "ArrayExpression", ["505"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["506", "507", "508", "509"], ["510", "511", "512", "513"], ["514", "515", "516", "517"], ["518", "519", "520", "521"], ["522", "523", "524", "525"], ["526", "527", "528", "529"], ["530", "531", "532", "533"], ["534", "535", "536", "537"], ["538", "539", "540", "541"], ["542", "543", "544", "545"], ["546", "547", "548", "549"], "React Hook useEffect has a missing dependency: 'fetchDomains'. Either include it or remove the dependency array.", ["550"], ["551", "552", "553", "554"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"messageId": "555", "data": "556", "fix": "557", "desc": "558"}, {"messageId": "555", "data": "559", "fix": "560", "desc": "561"}, {"messageId": "555", "data": "562", "fix": "563", "desc": "564"}, {"messageId": "555", "data": "565", "fix": "566", "desc": "567"}, {"messageId": "555", "data": "568", "fix": "569", "desc": "558"}, {"messageId": "555", "data": "570", "fix": "571", "desc": "561"}, {"messageId": "555", "data": "572", "fix": "573", "desc": "564"}, {"messageId": "555", "data": "574", "fix": "575", "desc": "567"}, {"desc": "576", "fix": "577"}, {"messageId": "555", "data": "578", "fix": "579", "desc": "580"}, {"messageId": "555", "data": "581", "fix": "582", "desc": "583"}, {"messageId": "555", "data": "584", "fix": "585", "desc": "586"}, {"messageId": "555", "data": "587", "fix": "588", "desc": "589"}, {"messageId": "555", "data": "590", "fix": "591", "desc": "580"}, {"messageId": "555", "data": "592", "fix": "593", "desc": "583"}, {"messageId": "555", "data": "594", "fix": "595", "desc": "586"}, {"messageId": "555", "data": "596", "fix": "597", "desc": "589"}, {"messageId": "555", "data": "598", "fix": "599", "desc": "580"}, {"messageId": "555", "data": "600", "fix": "601", "desc": "583"}, {"messageId": "555", "data": "602", "fix": "603", "desc": "586"}, {"messageId": "555", "data": "604", "fix": "605", "desc": "589"}, {"messageId": "555", "data": "606", "fix": "607", "desc": "580"}, {"messageId": "555", "data": "608", "fix": "609", "desc": "583"}, {"messageId": "555", "data": "610", "fix": "611", "desc": "586"}, {"messageId": "555", "data": "612", "fix": "613", "desc": "589"}, {"messageId": "555", "data": "614", "fix": "615", "desc": "558"}, {"messageId": "555", "data": "616", "fix": "617", "desc": "561"}, {"messageId": "555", "data": "618", "fix": "619", "desc": "564"}, {"messageId": "555", "data": "620", "fix": "621", "desc": "567"}, {"messageId": "555", "data": "622", "fix": "623", "desc": "558"}, {"messageId": "555", "data": "624", "fix": "625", "desc": "561"}, {"messageId": "555", "data": "626", "fix": "627", "desc": "564"}, {"messageId": "555", "data": "628", "fix": "629", "desc": "567"}, {"messageId": "555", "data": "630", "fix": "631", "desc": "558"}, {"messageId": "555", "data": "632", "fix": "633", "desc": "561"}, {"messageId": "555", "data": "634", "fix": "635", "desc": "564"}, {"messageId": "555", "data": "636", "fix": "637", "desc": "567"}, {"messageId": "555", "data": "638", "fix": "639", "desc": "558"}, {"messageId": "555", "data": "640", "fix": "641", "desc": "561"}, {"messageId": "555", "data": "642", "fix": "643", "desc": "564"}, {"messageId": "555", "data": "644", "fix": "645", "desc": "567"}, {"messageId": "555", "data": "646", "fix": "647", "desc": "558"}, {"messageId": "555", "data": "648", "fix": "649", "desc": "561"}, {"messageId": "555", "data": "650", "fix": "651", "desc": "564"}, {"messageId": "555", "data": "652", "fix": "653", "desc": "567"}, {"messageId": "555", "data": "654", "fix": "655", "desc": "558"}, {"messageId": "555", "data": "656", "fix": "657", "desc": "561"}, {"messageId": "555", "data": "658", "fix": "659", "desc": "564"}, {"messageId": "555", "data": "660", "fix": "661", "desc": "567"}, {"messageId": "555", "data": "662", "fix": "663", "desc": "558"}, {"messageId": "555", "data": "664", "fix": "665", "desc": "561"}, {"messageId": "555", "data": "666", "fix": "667", "desc": "564"}, {"messageId": "555", "data": "668", "fix": "669", "desc": "567"}, {"desc": "670", "fix": "671"}, {"messageId": "555", "data": "672", "fix": "673", "desc": "558"}, {"messageId": "555", "data": "674", "fix": "675", "desc": "561"}, {"messageId": "555", "data": "676", "fix": "677", "desc": "564"}, {"messageId": "555", "data": "678", "fix": "679", "desc": "567"}, "replaceWithAlt", {"alt": "680"}, {"range": "681", "text": "682"}, "Replace with `&apos;`.", {"alt": "683"}, {"range": "684", "text": "685"}, "Replace with `&lsquo;`.", {"alt": "686"}, {"range": "687", "text": "688"}, "Replace with `&#39;`.", {"alt": "689"}, {"range": "690", "text": "691"}, "Replace with `&rsquo;`.", {"alt": "680"}, {"range": "692", "text": "693"}, {"alt": "683"}, {"range": "694", "text": "695"}, {"alt": "686"}, {"range": "696", "text": "697"}, {"alt": "689"}, {"range": "698", "text": "699"}, "Update the dependencies array to be: [supabase]", {"range": "700", "text": "701"}, {"alt": "702"}, {"range": "703", "text": "704"}, "Replace with `&quot;`.", {"alt": "705"}, {"range": "706", "text": "707"}, "Replace with `&ldquo;`.", {"alt": "708"}, {"range": "709", "text": "710"}, "Replace with `&#34;`.", {"alt": "711"}, {"range": "712", "text": "713"}, "Replace with `&rdquo;`.", {"alt": "702"}, {"range": "714", "text": "715"}, {"alt": "705"}, {"range": "716", "text": "717"}, {"alt": "708"}, {"range": "718", "text": "719"}, {"alt": "711"}, {"range": "720", "text": "721"}, {"alt": "702"}, {"range": "722", "text": "723"}, {"alt": "705"}, {"range": "724", "text": "725"}, {"alt": "708"}, {"range": "726", "text": "727"}, {"alt": "711"}, {"range": "728", "text": "729"}, {"alt": "702"}, {"range": "730", "text": "731"}, {"alt": "705"}, {"range": "732", "text": "733"}, {"alt": "708"}, {"range": "734", "text": "735"}, {"alt": "711"}, {"range": "736", "text": "737"}, {"alt": "680"}, {"range": "738", "text": "739"}, {"alt": "683"}, {"range": "740", "text": "741"}, {"alt": "686"}, {"range": "742", "text": "743"}, {"alt": "689"}, {"range": "744", "text": "745"}, {"alt": "680"}, {"range": "746", "text": "739"}, {"alt": "683"}, {"range": "747", "text": "741"}, {"alt": "686"}, {"range": "748", "text": "743"}, {"alt": "689"}, {"range": "749", "text": "745"}, {"alt": "680"}, {"range": "750", "text": "751"}, {"alt": "683"}, {"range": "752", "text": "753"}, {"alt": "686"}, {"range": "754", "text": "755"}, {"alt": "689"}, {"range": "756", "text": "757"}, {"alt": "680"}, {"range": "758", "text": "759"}, {"alt": "683"}, {"range": "760", "text": "761"}, {"alt": "686"}, {"range": "762", "text": "763"}, {"alt": "689"}, {"range": "764", "text": "765"}, {"alt": "680"}, {"range": "766", "text": "767"}, {"alt": "683"}, {"range": "768", "text": "769"}, {"alt": "686"}, {"range": "770", "text": "771"}, {"alt": "689"}, {"range": "772", "text": "773"}, {"alt": "680"}, {"range": "774", "text": "775"}, {"alt": "683"}, {"range": "776", "text": "777"}, {"alt": "686"}, {"range": "778", "text": "779"}, {"alt": "689"}, {"range": "780", "text": "781"}, {"alt": "680"}, {"range": "782", "text": "783"}, {"alt": "683"}, {"range": "784", "text": "785"}, {"alt": "686"}, {"range": "786", "text": "787"}, {"alt": "689"}, {"range": "788", "text": "789"}, "Update the dependencies array to be: [fetchDomains, user]", {"range": "790", "text": "791"}, {"alt": "680"}, {"range": "792", "text": "793"}, {"alt": "683"}, {"range": "794", "text": "795"}, {"alt": "686"}, {"range": "796", "text": "797"}, {"alt": "689"}, {"range": "798", "text": "799"}, "&apos;", [2687, 2711], "Point &apos;CNAME' records to", "&lsquo;", [2687, 2711], "Point &lsquo;CNAME' records to", "&#39;", [2687, 2711], "Point &#39;CNAME' records to", "&rsquo;", [2687, 2711], "Point &rsquo;CNAME' records to", [2687, 2711], "Point 'CNAME&apos; records to", [2687, 2711], "Point 'CNAME&lsquo; records to", [2687, 2711], "Point 'CNAME&#39; records to", [2687, 2711], "Point 'CNAME&rsquo; records to", [3390, 3392], "[supabase]", "&quot;", [3966, 4015], "3. Click &quot;Check Status\" to verify task completion", "&ldquo;", [3966, 4015], "3. <PERSON>lick &ldquo;Check Status\" to verify task completion", "&#34;", [3966, 4015], "3. <PERSON><PERSON> &#34;Check Status\" to verify task completion", "&rdquo;", [3966, 4015], "3. <PERSON>lick &rdquo;Check Status\" to verify task completion", [3966, 4015], "3. <PERSON><PERSON> \"Check Status&quot; to verify task completion", [3966, 4015], "3. <PERSON>lick \"Check Status&ldquo; to verify task completion", [3966, 4015], "3. <PERSON>lick \"Check Status&#34; to verify task completion", [3966, 4015], "3. <PERSON>lick \"Check Status&rdquo; to verify task completion", [4035, 4096], "4. Only proceed with customization when status is &quot;completed\"", [4035, 4096], "4. Only proceed with customization when status is &ldquo;completed\"", [4035, 4096], "4. Only proceed with customization when status is &#34;completed\"", [4035, 4096], "4. Only proceed with customization when status is &rdquo;completed\"", [4035, 4096], "4. Only proceed with customization when status is \"completed&quot;", [4035, 4096], "4. Only proceed with customization when status is \"completed&ldquo;", [4035, 4096], "4. Only proceed with customization when status is \"completed&#34;", [4035, 4096], "4. Only proceed with customization when status is \"completed&rdquo;", [4676, 4777], "We provide innovative solutions to help your business grow and succeed in today&apos;s competitive market.", [4676, 4777], "We provide innovative solutions to help your business grow and succeed in today&lsquo;s competitive market.", [4676, 4777], "We provide innovative solutions to help your business grow and succeed in today&#39;s competitive market.", [4676, 4777], "We provide innovative solutions to help your business grow and succeed in today&rsquo;s competitive market.", [3551, 3652], [3551, 3652], [3551, 3652], [3551, 3652], [6377, 6448], "• After payment, you&apos;ll be able to map this domain to one of your sites", [6377, 6448], "• After payment, you&lsquo;ll be able to map this domain to one of your sites", [6377, 6448], "• After payment, you&#39;ll be able to map this domain to one of your sites", [6377, 6448], "• After payment, you&rsquo;ll be able to map this domain to one of your sites", [1779, 1895], "\n          Start typing to search for available domains. We&apos;ll show you real-time availability and pricing.\n        ", [1779, 1895], "\n          Start typing to search for available domains. We&lsquo;ll show you real-time availability and pricing.\n        ", [1779, 1895], "\n          Start typing to search for available domains. We&#39;ll show you real-time availability and pricing.\n        ", [1779, 1895], "\n          Start typing to search for available domains. We&rsquo;ll show you real-time availability and pricing.\n        ", [3179, 3313], "\n              Type your desired domain name above and we&apos;ll check availability across multiple extensions automatically.\n            ", [3179, 3313], "\n              Type your desired domain name above and we&lsquo;ll check availability across multiple extensions automatically.\n            ", [3179, 3313], "\n              Type your desired domain name above and we&#39;ll check availability across multiple extensions automatically.\n            ", [3179, 3313], "\n              Type your desired domain name above and we&rsquo;ll check availability across multiple extensions automatically.\n            ", [3119, 3173], "\n          Choose which site you&apos;d like to connect to ", [3119, 3173], "\n          Choose which site you&lsquo;d like to connect to ", [3119, 3173], "\n          Choose which site you&#39;d like to connect to ", [3119, 3173], "\n          Choose which site you&rsquo;d like to connect to ", [3629, 3695], " is now yours. Let&apos;s connect it to one of your sites.\n            ", [3629, 3695], " is now yours. Let&lsquo;s connect it to one of your sites.\n            ", [3629, 3695], " is now yours. Let&#39;s connect it to one of your sites.\n            ", [3629, 3695], " is now yours. Let&rsquo;s connect it to one of your sites.\n            ", [1082, 1088], "[fetchD<PERSON><PERSON>, user]", [4999, 5108], "\n            You haven&apos;t registered any domains yet. Start by registering your first domain below.\n          ", [4999, 5108], "\n            You haven&lsquo;t registered any domains yet. Start by registering your first domain below.\n          ", [4999, 5108], "\n            You haven&#39;t registered any domains yet. Start by registering your first domain below.\n          ", [4999, 5108], "\n            You haven&rsquo;t registered any domains yet. Start by registering your first domain below.\n          "]
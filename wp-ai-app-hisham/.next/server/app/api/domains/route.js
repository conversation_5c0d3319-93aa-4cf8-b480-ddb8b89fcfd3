/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/domains/route";
exports.ids = ["app/api/domains/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomains%2Froute&page=%2Fapi%2Fdomains%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomains%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomains%2Froute&page=%2Fapi%2Fdomains%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomains%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_WP_AI_wp_ai_app_hisham_src_app_api_domains_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/domains/route.ts */ \"(rsc)/./src/app/api/domains/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/domains/route\",\n        pathname: \"/api/domains\",\n        filename: \"route\",\n        bundlePath: \"app/api/domains/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/WP-AI/wp-ai-app-hisham/src/app/api/domains/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_WP_AI_wp_ai_app_hisham_src_app_api_domains_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/domains/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZkb21haW5zJTJGcm91dGUmcGFnZT0lMkZhcGklMkZkb21haW5zJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGZG9tYWlucyUyRnJvdXRlLnRzJmFwcERpcj0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZXUC1BSSUyRndwLWFpLWFwcC1oaXNoYW0lMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRmhvbWUlMkZkZWxsJTJGRGVza3RvcCUyRldQLUFJJTJGd3AtYWktYXBwLWhpc2hhbSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUNzQjtBQUNuRztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHVHQUF1RztBQUMvRztBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzZKOztBQUU3SiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvP2JmMGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL2hvbWUvZGVsbC9EZXNrdG9wL1dQLUFJL3dwLWFpLWFwcC1oaXNoYW0vc3JjL2FwcC9hcGkvZG9tYWlucy9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvZG9tYWlucy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2RvbWFpbnNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2RvbWFpbnMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvaG9tZS9kZWxsL0Rlc2t0b3AvV1AtQUkvd3AtYWktYXBwLWhpc2hhbS9zcmMvYXBwL2FwaS9kb21haW5zL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2RvbWFpbnMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgaGVhZGVySG9va3MsIHN0YXRpY0dlbmVyYXRpb25CYWlsb3V0LCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomains%2Froute&page=%2Fapi%2Fdomains%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomains%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/domains/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/domains/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Initialize Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n// GET /api/domains - Fetch all domains for the authenticated user\nasync function GET(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);\n        if (authError || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        // Fetch domains for the user with optional site information\n        const { data: domains, error } = await supabaseAdmin.from(\"domains\").select(`\n        *,\n        user-websites!site_id (\n          site_name\n        )\n      `).eq(\"user_id\", user.id).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"Error fetching domains:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch domains\"\n            }, {\n                status: 500\n            });\n        }\n        // Transform the data to include site_name at the top level\n        const transformedDomains = domains?.map((domain)=>({\n                ...domain,\n                site_name: domain[\"user-websites\"]?.site_name || null,\n                \"user-websites\": undefined // Remove the nested object\n            })) || [];\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            domains: transformedDomains\n        });\n    } catch (error) {\n        console.error(\"Domains API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/domains - Create a new domain record (used during registration process)\nasync function POST(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);\n        if (authError || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { domain_name, site_id, status = \"pending\", price_paid, currency = \"AUD\", stripe_session_id, namecheap_order_id, namecheap_transaction_id, registration_date, expiry_date, dns_configured = false, cname_target } = body;\n        if (!domain_name) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain name is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Insert the new domain record\n        const { data: domain, error } = await supabaseAdmin.from(\"domains\").insert({\n            domain_name,\n            user_id: user.id,\n            site_id,\n            status,\n            price_paid,\n            currency,\n            stripe_session_id,\n            namecheap_order_id,\n            namecheap_transaction_id,\n            registration_date,\n            expiry_date,\n            dns_configured,\n            cname_target\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating domain:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to create domain record\"\n            }, {\n                status: 500\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            domain\n        });\n    } catch (error) {\n        console.error(\"Domain creation error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/domains - Update an existing domain record\nasync function PATCH(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);\n        if (authError || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { domain_id, ...updates } = body;\n        if (!domain_id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Update the domain record (RLS will ensure user can only update their own domains)\n        const { data: domain, error } = await supabaseAdmin.from(\"domains\").update(updates).eq(\"id\", domain_id).eq(\"user_id\", user.id) // Extra security check\n        .select().single();\n        if (error) {\n            console.error(\"Error updating domain:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to update domain\"\n            }, {\n                status: 500\n            });\n        }\n        if (!domain) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain not found or access denied\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            domain\n        });\n    } catch (error) {\n        console.error(\"Domain update error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/domains/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomains%2Froute&page=%2Fapi%2Fdomains%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomains%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
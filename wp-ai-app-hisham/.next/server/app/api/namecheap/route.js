/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/namecheap/route";
exports.ids = ["app/api/namecheap/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_WP_AI_wp_ai_app_hisham_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/namecheap/route.ts */ \"(rsc)/./src/app/api/namecheap/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/namecheap/route\",\n        pathname: \"/api/namecheap\",\n        filename: \"route\",\n        bundlePath: \"app/api/namecheap/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/WP-AI/wp-ai-app-hisham/src/app/api/namecheap/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_WP_AI_wp_ai_app_hisham_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/namecheap/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/namecheap/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/namecheap/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/app/api/namecheap/route.ts\n\n\nconst COMMON_TLDS = [\n    {\n        tld: \"com\",\n        price: 12.98\n    },\n    {\n        tld: \"net\",\n        price: 14.98\n    },\n    {\n        tld: \"org\",\n        price: 14.98\n    },\n    {\n        tld: \"info\",\n        price: 18.98\n    },\n    {\n        tld: \"biz\",\n        price: 18.98\n    },\n    {\n        tld: \"com.au\",\n        price: 16.50\n    },\n    {\n        tld: \"net.au\",\n        price: 16.50\n    },\n    {\n        tld: \"org.au\",\n        price: 16.50\n    },\n    {\n        tld: \"co\",\n        price: 32.98\n    },\n    {\n        tld: \"io\",\n        price: 59.98\n    }\n];\nconst USD_TO_AUD_RATE = 1.5;\n// ... (checkDomainAvailability, generateDomainVariations, getPriceForDomain functions are unchanged)\nasync function checkDomainAvailability(domainList) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    if (!apiUser || !apiKey || !username || !clientIp) {\n        throw new Error(\"Missing Namecheap API configuration\");\n    }\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.check\",\n        ClientIp: clientIp,\n        DomainList: domainList.join(\",\")\n    });\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        const xmlText = await response.text();\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) throw new Error(`Namecheap API error: ${errorMatch[1]}`);\n        const domainResults = [];\n        const domainRegex = /<DomainCheckResult[^>]*Domain=\"([^\"]*)\"[^>]*Available=\"([^\"]*)\"[^>]*IsPremiumName=\"([^\"]*)\"[^>]*(?:PremiumRegistrationPrice=\"([^\"]*)\")?[^>]*\\/>/g;\n        let match;\n        while((match = domainRegex.exec(xmlText)) !== null){\n            const [, domainName, available, isPremium, premiumPrice] = match;\n            const result = {\n                Domain: domainName,\n                Available: available === \"true\",\n                IsPremiumName: isPremium === \"true\"\n            };\n            if (result.IsPremiumName && premiumPrice) result.PremiumRegistrationPrice = parseFloat(premiumPrice);\n            domainResults.push(result);\n        }\n        return domainResults;\n    } catch (error) {\n        console.error(\"Namecheap API error:\", error);\n        throw error;\n    }\n}\nfunction generateDomainVariations(baseDomain) {\n    const domainName = baseDomain.replace(/\\.(com|net|org|info|biz|com\\.au|net\\.au|org\\.au|co|io)$/i, \"\");\n    return COMMON_TLDS.map(({ tld })=>`${domainName}.${tld}`);\n}\nfunction getPriceForDomain(domain, namecheapResult) {\n    if (namecheapResult.IsPremiumName && namecheapResult.PremiumRegistrationPrice) {\n        return namecheapResult.PremiumRegistrationPrice * USD_TO_AUD_RATE + 5;\n    }\n    const tld = domain.split(\".\").slice(1).join(\".\");\n    const tldInfo = COMMON_TLDS.find((t)=>t.tld === tld);\n    if (tldInfo) return tldInfo.price * USD_TO_AUD_RATE + 5;\n    return 20 * USD_TO_AUD_RATE + 5;\n}\n// ✨ NEW FUNCTION: To register a domain with Namecheap\nasync function registerDomain(domainName) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    // Registrant contact information sourced from environment variables\n    const registrant = {\n        FirstName: process.env.REGISTRANT_FIRST_NAME,\n        LastName: process.env.REGISTRANT_LAST_NAME,\n        Address1: process.env.REGISTRANT_ADDRESS1,\n        City: process.env.REGISTRANT_CITY,\n        StateProvince: process.env.REGISTRANT_STATE_PROVINCE,\n        PostalCode: process.env.REGISTRANT_POSTAL_CODE,\n        Country: process.env.REGISTRANT_COUNTRY,\n        Phone: process.env.REGISTRANT_PHONE,\n        EmailAddress: process.env.REGISTRANT_EMAIL\n    };\n    for (const [key, value] of Object.entries(registrant)){\n        if (!value) {\n            throw new Error(`Missing required registrant environment variable: REGISTRANT_${key.toUpperCase()}`);\n        }\n    }\n    // ✨ FIX: Correctly map the registrant fields to the required API parameters.\n    // The previous code used `...registrant` which resulted in sending `FirstName` instead of `RegistrantFirstName`.\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.create\",\n        ClientIp: clientIp,\n        DomainName: domainName,\n        Years: \"1\",\n        // Registrant contact details\n        RegistrantFirstName: registrant.FirstName,\n        RegistrantLastName: registrant.LastName,\n        RegistrantAddress1: registrant.Address1,\n        RegistrantCity: registrant.City,\n        RegistrantStateProvince: registrant.StateProvince,\n        RegistrantPostalCode: registrant.PostalCode,\n        RegistrantCountry: registrant.Country,\n        RegistrantPhone: registrant.Phone,\n        RegistrantEmailAddress: registrant.EmailAddress,\n        // Admin, Tech, and AuxBilling contacts (using the same details)\n        AdminFirstName: registrant.FirstName,\n        AdminLastName: registrant.LastName,\n        AdminAddress1: registrant.Address1,\n        AdminCity: registrant.City,\n        AdminStateProvince: registrant.StateProvince,\n        AdminPostalCode: registrant.PostalCode,\n        AdminCountry: registrant.Country,\n        AdminPhone: registrant.Phone,\n        AdminEmailAddress: registrant.EmailAddress,\n        TechFirstName: registrant.FirstName,\n        TechLastName: registrant.LastName,\n        TechAddress1: registrant.Address1,\n        TechCity: registrant.City,\n        TechStateProvince: registrant.StateProvince,\n        TechPostalCode: registrant.PostalCode,\n        TechCountry: registrant.Country,\n        TechPhone: registrant.Phone,\n        TechEmailAddress: registrant.EmailAddress,\n        AuxBillingFirstName: registrant.FirstName,\n        AuxBillingLastName: registrant.LastName,\n        AuxBillingAddress1: registrant.Address1,\n        AuxBillingCity: registrant.City,\n        AuxBillingStateProvince: registrant.StateProvince,\n        AuxBillingPostalCode: registrant.PostalCode,\n        AuxBillingCountry: registrant.Country,\n        AuxBillingPhone: registrant.Phone,\n        AuxBillingEmailAddress: registrant.EmailAddress\n    });\n    // Add specific parameters for .au domains\n    if (domainName.endsWith(\".au\")) {\n        const registrantIdNumber = process.env.AU_REGISTRANT_ID_NUMBER;\n        const registrantIdType = process.env.AU_REGISTRANT_ID_TYPE;\n        if (!registrantIdNumber || !registrantIdType) {\n            throw new Error(\"Missing required environment variables for .au domain registration: AU_REGISTRANT_ID_NUMBER and AU_REGISTRANT_ID_TYPE\");\n        }\n        params.append(\"au_RegistrantIdNumber\", registrantIdNumber);\n        params.append(\"au_RegistrantIdType\", registrantIdType);\n    }\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"POST\"\n        });\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        const xmlText = await response.text();\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) {\n            throw new Error(`Namecheap API Error: ${errorMatch[1]}`);\n        }\n        const successMatch = xmlText.match(/<DomainCreateResult[^>]*Registered=\"true\"[^>]*>/);\n        if (!successMatch) {\n            // Provide a more detailed error if registration is not explicitly confirmed\n            throw new Error(`Domain registration failed. The response from Namecheap did not confirm success. XML: ${xmlText}`);\n        }\n        const orderIdMatch = xmlText.match(/OrderID=\"(\\d+)\"/);\n        const transactionIdMatch = xmlText.match(/TransactionID=\"(\\d+)\"/);\n        return {\n            success: true,\n            message: `Domain ${domainName} registered successfully.`,\n            orderId: orderIdMatch ? orderIdMatch[1] : null,\n            transactionId: transactionIdMatch ? transactionIdMatch[1] : null\n        };\n    } catch (error) {\n        console.error(\"Namecheap registration error:\", error);\n        throw error;\n    }\n}\n// Helper to get Supabase client (service role)\nfunction getSupabaseServiceClient() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n}\n// Function to set CNAME record via Namecheap API\nasync function setCNAMERecord(domain, cnameValue) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    // Namecheap API for DNS setHosts\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.dns.setHosts\",\n        ClientIp: clientIp,\n        SLD: domain.split(\".\")[0],\n        TLD: domain.split(\".\").slice(1).join(\".\"),\n        \"HostName1\": \"@\",\n        \"RecordType1\": \"CNAME\",\n        \"Address1\": cnameValue,\n        \"TTL1\": \"1800\"\n    });\n    const response = await fetch(`${baseUrl}?${params.toString()}`, {\n        method: \"POST\"\n    });\n    const xmlText = await response.text();\n    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n    if (errorMatch) {\n        throw new Error(`Namecheap DNS Error: ${errorMatch[1]}`);\n    }\n    const successMatch = xmlText.match(/<DomainDNSSetHostsResult[^>]*IsSuccess=\"true\"/);\n    if (!successMatch) {\n        throw new Error(`CNAME mapping failed. XML: ${xmlText}`);\n    }\n    return {\n        success: true,\n        message: `CNAME for ${domain} -> ${cnameValue} set successfully.`\n    };\n}\nasync function POST(request) {\n    try {\n        const requestBody = await request.json();\n        const { domain, action, siteId, userId, stripeSessionId } = requestBody;\n        if (!domain) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"check\") {\n            const domainVariations = generateDomainVariations(domain);\n            const namecheapResults = await checkDomainAvailability(domainVariations);\n            const results = namecheapResults.map((result)=>{\n                const formattedResult = {\n                    Domain: result.Domain,\n                    Available: result.Available,\n                    IsPremiumName: result.IsPremiumName\n                };\n                if (result.Available) {\n                    formattedResult.Price = getPriceForDomain(result.Domain, result);\n                }\n                return formattedResult;\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                results\n            });\n        }\n        if (action === \"register\") {\n            if (!userId) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"userId is required for registration\"\n                }, {\n                    status: 400\n                });\n            }\n            const supabase = getSupabaseServiceClient();\n            // Handle site mapping - if siteId is 'pending', we'll register the domain without site mapping\n            let siteName = null;\n            let finalSiteId = null;\n            if (siteId && siteId !== \"pending\") {\n                // Fetch site_name from Supabase\n                console.log(\"[Namecheap API] Fetching site_name for siteId:\", siteId);\n                const { data: site, error: siteError } = await supabase.from(\"user-websites\").select(\"site_name\").eq(\"id\", siteId).single();\n                if (siteError || !site) {\n                    console.error(\"[Namecheap API] Could not fetch site_name for siteId:\", siteId, siteError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Could not fetch site_name for siteId\"\n                    }, {\n                        status: 400\n                    });\n                }\n                siteName = site.site_name;\n                finalSiteId = siteId;\n                console.log(\"[Namecheap API] site_name fetched:\", siteName);\n            } else {\n                console.log(\"[Namecheap API] Registering domain without site mapping (siteId is pending)\");\n            }\n            // Create or update domain record in database\n            const domainData = {\n                domain_name: domain,\n                user_id: userId,\n                site_id: finalSiteId,\n                status: \"pending\",\n                stripe_session_id: stripeSessionId,\n                cname_target: siteName\n            };\n            // Check if domain record already exists\n            const { data: existingDomain } = await supabase.from(\"domains\").select(\"id\").eq(\"domain_name\", domain).eq(\"user_id\", userId).single();\n            let domainRecord;\n            if (existingDomain) {\n                // Update existing domain record\n                const { data, error: updateError } = await supabase.from(\"domains\").update({\n                    ...domainData,\n                    status: \"pending\"\n                }).eq(\"id\", existingDomain.id).select().single();\n                if (updateError) {\n                    console.error(\"[Namecheap API] Failed to update domain record:\", updateError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Failed to update domain record\"\n                    }, {\n                        status: 500\n                    });\n                }\n                domainRecord = data;\n            } else {\n                // Create new domain record\n                const { data, error: insertError } = await supabase.from(\"domains\").insert(domainData).select().single();\n                if (insertError) {\n                    console.error(\"[Namecheap API] Failed to create domain record:\", insertError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Failed to create domain record\"\n                    }, {\n                        status: 500\n                    });\n                }\n                domainRecord = data;\n            }\n            // Register domain with Namecheap\n            console.log(\"[Namecheap API] Registering domain:\", domain);\n            let registrationResult;\n            try {\n                registrationResult = await registerDomain(domain);\n                console.log(\"[Namecheap API] Registration result:\", registrationResult);\n                // Update domain record with registration details\n                const registrationDate = new Date().toISOString();\n                const expiryDate = new Date();\n                expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year from now\n                await supabase.from(\"domains\").update({\n                    status: \"registered\",\n                    registration_date: registrationDate,\n                    expiry_date: expiryDate.toISOString(),\n                    namecheap_order_id: registrationResult.orderId,\n                    namecheap_transaction_id: registrationResult.transactionId\n                }).eq(\"id\", domainRecord.id);\n            } catch (regError) {\n                console.error(\"[Namecheap API] Registration failed:\", regError);\n                // Update domain record to failed status\n                await supabase.from(\"domains\").update({\n                    status: \"failed\"\n                }).eq(\"id\", domainRecord.id);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: regError instanceof Error ? regError.message : \"Domain registration failed\"\n                }, {\n                    status: 500\n                });\n            }\n            // Set CNAME record and update site mapping only if we have a site\n            let cnameResult = null;\n            let siteNameUpdated = false;\n            if (siteName && finalSiteId) {\n                try {\n                    console.log(\"[Namecheap API] Setting CNAME for domain:\", domain, \"to:\", siteName);\n                    cnameResult = await setCNAMERecord(domain, siteName);\n                    console.log(\"[Namecheap API] CNAME mapping result:\", cnameResult);\n                    // Update domain record with DNS configuration\n                    await supabase.from(\"domains\").update({\n                        dns_configured: true,\n                        status: \"active\"\n                    }).eq(\"id\", domainRecord.id);\n                    // Update site_name to the new domain\n                    console.log(\"[Namecheap API] Updating site_name for siteId:\", finalSiteId, \"to:\", domain);\n                    const { error: updateError } = await supabase.from(\"user-websites\").update({\n                        site_name: domain\n                    }).eq(\"id\", finalSiteId);\n                    if (updateError) {\n                        console.error(\"[Namecheap API] Failed to update site_name:\", updateError);\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            ...registrationResult,\n                            cnameResult,\n                            updateError: updateError.message\n                        }, {\n                            status: 500\n                        });\n                    }\n                    console.log(\"[Namecheap API] site_name updated successfully.\");\n                    siteNameUpdated = true;\n                } catch (cnameErr) {\n                    console.error(\"[Namecheap API] CNAME mapping error:\", cnameErr);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        ...registrationResult,\n                        cnameError: cnameErr instanceof Error ? cnameErr.message : cnameErr\n                    }, {\n                        status: 500\n                    });\n                }\n            } else {\n                console.log(\"[Namecheap API] Skipping CNAME setup - no site mapping provided\");\n                // Update domain status to registered (not active since no DNS setup)\n                await supabase.from(\"domains\").update({\n                    status: \"registered\"\n                }).eq(\"id\", domainRecord.id);\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                ...registrationResult,\n                cnameResult,\n                siteNameUpdated,\n                domainRecord,\n                message: siteName ? \"Domain registered and mapped to site\" : \"Domain registered successfully (no site mapping)\"\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Invalid action\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error instanceof Error ? error.message : \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/namecheap/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2FWP-AI%2Fwp-ai-app-hisham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jose";
exports.ids = ["vendor-chunks/jose"];
exports.modules = {

/***/ "(ssr)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/lib/buffer_utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatKdf: () => (/* binding */ concatKdf),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   lengthAndInput: () => (/* binding */ lengthAndInput),\n/* harmony export */   p2s: () => (/* binding */ p2s),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/digest.js */ \"(ssr)/./node_modules/jose/dist/node/esm/runtime/digest.js\");\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length })=>acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    buffers.forEach((buffer)=>{\n        buf.set(buffer, i);\n        i += buffer.length;\n    });\n    return buf;\n}\nfunction p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([\n        0\n    ]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([\n        value >>> 24,\n        value >>> 16,\n        value >>> 8,\n        value & 0xff\n    ], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for(let iter = 0; iter < iterations; iter++){\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sha256\", buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jose/dist/node/esm/runtime/base64url.js":
/*!**************************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/base64url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(ssr)/./node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nlet encode;\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.decoder.decode(encoded);\n    }\n    return encoded;\n}\nif (buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.isEncoding(\"base64url\")) {\n    encode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64url\");\n} else {\n    encode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64\").replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\nconst decodeBase64 = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input, \"base64\");\nconst encodeBase64 = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString(\"base64\");\n\nconst decode = (input)=>buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(normalize(input), \"base64\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/runtime/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jose/dist/node/esm/runtime/digest.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/runtime/digest.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst digest = (algorithm, data)=>(0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(algorithm).update(data).digest();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (digest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBQ3BDLE1BQU1DLFNBQVMsQ0FBQ0MsV0FBV0MsT0FBU0gsa0RBQVVBLENBQUNFLFdBQVdFLE1BQU0sQ0FBQ0QsTUFBTUYsTUFBTTtBQUM3RSxpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3J1bnRpbWUvZGlnZXN0LmpzP2ZjMzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlSGFzaCB9IGZyb20gJ2NyeXB0byc7XG5jb25zdCBkaWdlc3QgPSAoYWxnb3JpdGhtLCBkYXRhKSA9PiBjcmVhdGVIYXNoKGFsZ29yaXRobSkudXBkYXRlKGRhdGEpLmRpZ2VzdCgpO1xuZXhwb3J0IGRlZmF1bHQgZGlnZXN0O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUhhc2giLCJkaWdlc3QiLCJhbGdvcml0aG0iLCJkYXRhIiwidXBkYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/runtime/digest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jose/dist/node/esm/util/base64url.js":
/*!***********************************************************!*\
  !*** ./node_modules/jose/dist/node/esm/util/base64url.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(ssr)/./node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\nconst encode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.encode;\nconst decode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.decode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3V0aWwvYmFzZTY0dXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM5QyxNQUFNQyxTQUFTRCx5REFBZ0IsQ0FBQztBQUNoQyxNQUFNRSxTQUFTRix5REFBZ0IsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvam9zZS9kaXN0L25vZGUvZXNtL3V0aWwvYmFzZTY0dXJsLmpzPzllY2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgYmFzZTY0dXJsIGZyb20gJy4uL3J1bnRpbWUvYmFzZTY0dXJsLmpzJztcbmV4cG9ydCBjb25zdCBlbmNvZGUgPSBiYXNlNjR1cmwuZW5jb2RlO1xuZXhwb3J0IGNvbnN0IGRlY29kZSA9IGJhc2U2NHVybC5kZWNvZGU7XG4iXSwibmFtZXMiOlsiYmFzZTY0dXJsIiwiZW5jb2RlIiwiZGVjb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jose/dist/node/esm/util/base64url.js\n");

/***/ })

};
;
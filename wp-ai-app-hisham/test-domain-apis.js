// Test script for domain management APIs
// Run with: node test-domain-apis.js

const BASE_URL = 'http://localhost:3000';

// Test domain search API
async function testDomainSearch() {
  console.log('🔍 Testing domain search API...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/namecheap`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        domain: 'test-domain-123', 
        action: 'check' 
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Domain search API working');
      console.log(`   Found ${data.results?.length || 0} domain variations`);
      if (data.results?.length > 0) {
        console.log(`   Example: ${data.results[0].Domain} - Available: ${data.results[0].Available}`);
      }
    } else {
      console.log('❌ Domain search API failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Domain search API error:', error.message);
  }
}

// Test payment verification API
async function testPaymentVerification() {
  console.log('\n💳 Testing payment verification API...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/verify-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        sessionId: 'cs_test_fake_session_id' 
      }),
    });

    const data = await response.json();
    
    if (response.status === 500 && data.error.includes('No such checkout session')) {
      console.log('✅ Payment verification API working (expected error for fake session ID)');
    } else if (response.ok) {
      console.log('✅ Payment verification API working');
      console.log(`   Session status: ${data.status}`);
    } else {
      console.log('❌ Payment verification API failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Payment verification API error:', error.message);
  }
}

// Test domains API (requires authentication)
async function testDomainsAPI() {
  console.log('\n📋 Testing domains API...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/domains`, {
      method: 'GET',
      headers: { 
        'Content-Type': 'application/json',
        // Note: This will fail without proper auth token
        'Authorization': 'Bearer fake_token'
      },
    });

    const data = await response.json();
    
    if (response.status === 401 && data.error === 'Not authenticated') {
      console.log('✅ Domains API working (expected auth error)');
    } else if (response.ok) {
      console.log('✅ Domains API working');
      console.log(`   Found ${data.domains?.length || 0} domains`);
    } else {
      console.log('❌ Domains API failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Domains API error:', error.message);
  }
}

// Test domain checkout session API (requires authentication)
async function testDomainCheckout() {
  console.log('\n🛒 Testing domain checkout API...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/domain-checkout-session`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        // Note: This will fail without proper auth token
        'Authorization': 'Bearer fake_token'
      },
      body: JSON.stringify({
        domain: 'test-domain.com',
        price: 15.00,
        siteId: 'pending'
      }),
    });

    const data = await response.json();
    
    if (response.status === 401 && data.error === 'Not authenticated') {
      console.log('✅ Domain checkout API working (expected auth error)');
    } else if (response.ok) {
      console.log('✅ Domain checkout API working');
      console.log(`   Checkout URL: ${data.url ? 'Generated' : 'Not generated'}`);
    } else {
      console.log('❌ Domain checkout API failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Domain checkout API error:', error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Starting Domain Management API Tests\n');
  console.log('Note: Some tests expect authentication errors for security validation\n');
  
  await testDomainSearch();
  await testPaymentVerification();
  await testDomainsAPI();
  await testDomainCheckout();
  
  console.log('\n✨ Test suite completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Apply database migrations if not done already');
  console.log('2. Test the UI workflow in the browser');
  console.log('3. Try a real domain registration with test Stripe keys');
}

// Check if we're running in Node.js environment
if (typeof window === 'undefined') {
  // Node.js environment - need to install node-fetch
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    runTests();
  } catch (error) {
    console.log('❌ Please install node-fetch: npm install node-fetch');
    console.log('Or run this script in a browser console instead.');
  }
} else {
  // Browser environment
  runTests();
}

# Database Setup for Stripe Integration

This guide explains how to set up the database schema changes required for the Stripe-Supabase integration.

## Required Changes

### 1. Add stripe_customer_id Column to Profiles Table

You need to add a `stripe_customer_id` column to your existing `profiles` table. This will store the Stripe customer ID for each user.

#### Option A: Using Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Run the following SQL:

```sql
-- Add stripe_customer_id column to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- Add a unique constraint to ensure one-to-one mapping
ALTER TABLE profiles 
ADD CONSTRAINT unique_stripe_customer_id 
UNIQUE (stripe_customer_id);

-- Add an index for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id 
ON profiles (stripe_customer_id);

-- Add a comment to document the column
COMMENT ON COLUMN profiles.stripe_customer_id IS 'Stripe customer ID for billing and subscription management';
```

#### Option B: Using Migration File

If you're using migrations, you can run the provided migration file:

```bash
# The migration file is located at: migrations/add_stripe_customer_id_to_profiles.sql
# Apply it using your preferred migration tool or Supabase CLI
```

### 2. Verify the Schema

After running the migration, your `profiles` table should have the following structure:

```sql
-- Example profiles table structure
CREATE TABLE profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  first_name TEXT,
  last_name TEXT,
  email TEXT UNIQUE NOT NULL,
  phone TEXT,
  address1 TEXT,
  city TEXT,
  state_province TEXT,
  postal_code TEXT,
  country TEXT,
  join_date TIMESTAMP,
  avatar TEXT,
  stripe_customer_id TEXT UNIQUE, -- New column
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Environment Variables

Make sure you have the following environment variables set in your `.env.local`:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

## How It Works

### Customer Creation Flow

1. **User Authentication**: When a user tries to create a checkout session, the system first verifies their Supabase authentication.

2. **Customer Lookup**: The system checks if the user already has a `stripe_customer_id` in their profile.

3. **Customer Creation**: If no Stripe customer exists, the system:
   - Creates a new Stripe customer using the user's profile data
   - Stores the Stripe customer ID in the `profiles.stripe_customer_id` column

4. **Checkout Session**: The checkout session is created with the user's Stripe customer ID.

### Data Flow

```
User Profile (Supabase) ←→ Stripe Customer
     ↓                           ↓
Profile Data              Customer Data
- first_name              - name
- last_name               - email  
- email                   - phone
- phone                   - address
- address fields          
- stripe_customer_id ←----→ customer.id
```

### Benefits of This Approach

1. **Data Integrity**: Database constraints ensure one-to-one mapping
2. **Performance**: Indexed lookups are fast
3. **Consistency**: All user data is centralized in the profiles table
4. **Flexibility**: Easy to add more billing-related fields in the future
5. **Reliability**: Automatic customer creation when needed

## Testing the Integration

### 1. Test Customer Creation

```javascript
// Test the customer creation endpoint
const response = await fetch('/api/stripe/customer', {
  headers: {
    'Authorization': `Bearer ${session.access_token}`
  }
});
const data = await response.json();
console.log('Stripe Customer ID:', data.stripe_customer_id);
```

### 2. Test Checkout Session

```javascript
// Test checkout session creation
const response = await fetch('/api/create-checkout-session', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session.access_token}`
  },
  body: JSON.stringify({
    planId: 'starter',
    isYearly: false,
    siteId: 'test-site'
  })
});
```

### 3. Test Billing Data

```javascript
// Test billing data retrieval
const response = await fetch('/api/stripe/billing-data', {
  headers: {
    'Authorization': `Bearer ${session.access_token}`
  }
});
const billingData = await response.json();
```

## Troubleshooting

### Common Issues

1. **"Column already exists" error**: The column might already exist. Check your table schema.

2. **Permission denied**: Make sure you're using the service role key for server-side operations.

3. **Customer creation fails**: Check that all required environment variables are set.

4. **Authentication errors**: Ensure the frontend is sending the correct authorization headers.

### Verification Queries

```sql
-- Check if the column exists
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' AND column_name = 'stripe_customer_id';

-- Check for existing Stripe customer IDs
SELECT email, stripe_customer_id 
FROM profiles 
WHERE stripe_customer_id IS NOT NULL;

-- Verify constraints
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'profiles' AND constraint_name = 'unique_stripe_customer_id';
```

export const plans = {
  starter: {
    monthly: 'price_1Rh6gZHKmibvltr1B4VM6bW3',
    yearly: 'price_1Rl1IwHKmibvltr1UqjhoNk8',
  },
  pro: {
    monthly: 'price_1PLaO5HKmibvltr1AFSWYvYl',
    yearly: 'price_1PLaO5HKmibvltr1Q22gVz8P',
  },
  turbo: {
    monthly: 'price_1PLaOKHKmibvltr18ySjBqDB',
    yearly: 'price_1PLaOKHKmibvltr1yC4hTRs8',
  },
};

export const getPriceId = (planId: string, isYearly: boolean): string => {
  const plan = plans[planId];
  if (!plan) {
    throw new Error(`Plan not found: ${planId}`);
  }
  return isYearly ? plan.yearly : plan.monthly;
};

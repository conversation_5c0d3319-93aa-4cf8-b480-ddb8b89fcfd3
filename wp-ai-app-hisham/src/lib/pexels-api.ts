// Pexels API integration

export interface PexelsImage {
  id: string;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  liked: boolean;
  alt: string;
}

export interface PexelsSearchResponse {
  total_results: number;
  page: number;
  per_page: number;
  photos: PexelsImage[];
  next_page: string;
}

export async function searchPexelsImages(query: string, perPage: number = 15): Promise<PexelsImage[]> {
  try {
    // Instead of calling Pexels API directly, use our server-side API route
    // This avoids CORS issues and keeps the API key secure on the server
    const response = await fetch(
      `/api/images?query=${encodeURIComponent(query)}&perPage=${perPage}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
    }

    const data: PexelsSearchResponse = await response.json();
    return data.photos || [];
  } catch (error) {
    console.error("Error fetching images:", error);




















































































    
    return [];
  }
}

export async function extractKeywordsFromConversation(messages: any[]): Promise<string[]> {
  try {
    // Call our AI-powered keyword extraction endpoint
    const response = await fetch('/api/extract-keywords', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ messages })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();

    // If we got keywords from the AI, use them
    if (data.keywords && Array.isArray(data.keywords) && data.keywords.length > 0) {
      console.log("AI extracted keywords:", data.keywords);
      return data.keywords;
    }

    // Fall back to the original method if AI extraction fails
    return fallbackKeywordExtraction(messages);
  } catch (error) {
    console.error("Error extracting keywords with AI:", error);
    // Fall back to the original method
    return fallbackKeywordExtraction(messages);
  }
}

// Keep the original method as a fallback
function fallbackKeywordExtraction(messages: any[]): string[] {
  console.log("Using fallback keyword extraction");
  // Extract keywords from the last user message
  const lastUserMessage = [...messages].reverse().find(msg => msg.role === "user");

  if (!lastUserMessage || !lastUserMessage.content) {
    return ["business"];
  }

  const content = typeof lastUserMessage.content === "string"
    ? lastUserMessage.content
    : lastUserMessage.content.map((c: any) => (typeof c === "string" ? c : c.text || "")).join(" ");

  // Simple keyword extraction - remove common words and punctuation
  const commonWords = ["and", "the", "is", "in", "to", "of", "for", "with", "a", "an", "our", "we", "i", "my"];
  const words = content
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3 && !commonWords.includes(word));

  // Get unique keywords - without using Set spread to avoid compatibility issues
  const uniqueWords: string[] = [];
  for (const word of words) {
    if (!uniqueWords.includes(word)) {
      uniqueWords.push(word);
    }
  }

  // Return up to 3 most relevant keywords, or fallback to "business" if none found
  return uniqueWords.slice(0, 3).length > 0 ? uniqueWords.slice(0, 3) : ["business"];
}

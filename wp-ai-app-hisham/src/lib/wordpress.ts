/**
 * WordPress API utility functions
 */

/**
 * Fetch a list of WordPress themes based on keywords
 * @param keywords Array of keywords to search for
 * @param page Page number
 * @param perPage Number of themes per page
 * @returns Promise with theme data
 */
export async function fetchThemes(
  keywords: string[],
  page: number = 1,
  perPage: number = 12
): Promise<{
  info: {
    page: number;
    pages: number;
    results: number;
  };
  themes: any[];
  error?: string;
}> {
  try {
    const keywordsString = keywords.join(',');
    const res = await fetch(
      `/api/wordpress-themes?keywords=${encodeURIComponent(keywordsString)}&page=${page}&perPage=${perPage}`,
      {
        cache: 'no-store'
      }
    );

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.error || `Failed to fetch themes (${res.status})`);
    }

    const data = await res.json();
    return data;
  } catch (error: any) {
    console.error('Error fetching themes:', error);
    return {
      info: { page: 1, pages: 0, results: 0 },
      themes: [],
      error: error.message || 'Failed to fetch themes'
    };
  }
}

/**
 * Fetch details for a specific WordPress theme
 * @param themeId Theme slug/ID
 * @returns Promise with theme details
 */
export async function fetchThemeDetails(themeId: string): Promise<any> {
  try {
    const res = await fetch(`/api/wordpress-theme/${themeId}`, {
      cache: 'no-store'
    });

    const data = await res.json();

    // Check if we got a fallback response (which will have status 200 but might include _error)
    if (data._error && data._error.fallback) {
      console.log('Received fallback theme data with error:', data._error.message);
      // We still use the fallback data, but we can log it
      return data;
    } else if (!res.ok) {
      throw new Error(data.error || `Failed to fetch theme details (${res.status})`);
    } else if (data.error) {
      throw new Error(data.error);
    }

    return data;
  } catch (error: any) {
    console.error('Error fetching theme details:', error);
    throw error;
  }
}

/**
 * Format a date string
 * @param dateString Date string
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  if (!dateString) return 'Unknown';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format a rating value (0-100) to a 5-star scale
 * @param rating Rating value (0-100)
 * @returns Formatted rating string
 */
export function formatRating(rating: number): string {
  return rating ? (rating / 100 * 5).toFixed(1) : 'No ratings';
}

/**
 * Format download count with K/M suffixes
 * @param downloads Download count
 * @returns Formatted download count string
 */
export function formatDownloads(downloads: number): string {
  if (!downloads) return 'Unknown';
  if (downloads >= 1000000) {
    return `${(downloads / 1000000).toFixed(1)}M+`;
  } else if (downloads >= 1000) {
    return `${(downloads / 1000).toFixed(1)}K+`;
  }
  return downloads.toString();
}

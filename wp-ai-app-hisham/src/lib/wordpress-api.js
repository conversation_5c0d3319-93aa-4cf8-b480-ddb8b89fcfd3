/**
 * WordPress API Service Layer
 * Handles all WordPress REST API communication for theme customization
 */

// Configuration
const WP_BASE_URL = 'http://192.168.10.193/wordpress';
const WP_API_BASE = `${WP_BASE_URL}/wp-json`;
const CUSTOMIZER_API_BASE = `${WP_API_BASE}/customizer/v1`;

// Default headers for API requests
const getDefaultHeaders = () => ({
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  // Add CORS headers
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
});

// Authentication headers (to be implemented with WordPress Application Passwords)
const getAuthHeaders = () => {
  // TODO: Implement authentication with WordPress Application Passwords or JWT
  // For now, return empty object - will be enhanced when WordPress backend is ready
  return {};
};

/**
 * Generic API request handler with error handling and retry logic
 */
const apiRequest = async (url, options = {}, retries = 3) => {
  const defaultOptions = {
    headers: {
      ...getDefaultHeaders(),
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  const requestOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, requestOptions);

      // Handle different response types
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      // Return JSON response
      return await response.json();
    } catch (error) {
      console.error(`API request attempt ${attempt} failed:`, error);

      // If this is the last attempt, throw the error
      if (attempt === retries) {
        throw new Error(`API request failed after ${retries} attempts: ${error.message}`);
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

/**
 * Theme Customization API Functions
 */

/**
 * Retrieve current customizations for a theme
 * @param {string} themeId - The theme identifier
 * @returns {Promise<Object>} Current customization settings
 */
export const getThemeCustomizations = async (themeId) => {
  try {
    const url = `${CUSTOMIZER_API_BASE}/themes/${themeId}`;
    return await apiRequest(url);
  } catch (error) {
    console.error('Failed to get theme customizations:', error);
    // Return default customizations if API fails
    return {
      colors: {
        primary: '#3b82f6',
        secondary: '#6366f1',
        accent: '#ec4899'
      },
      fonts: {
        primary: 'Inter',
        secondary: 'Open Sans',
        pair_id: 'inter-opensans'
      },
      logo: null
    };
  }
};

/**
 * Save customizations for a theme
 * @param {string} themeId - The theme identifier
 * @param {Object} customizations - The customization settings to save
 * @returns {Promise<Object>} Save operation result
 */
export const saveThemeCustomizations = async (themeId, customizations) => {
  try {
    const url = `${CUSTOMIZER_API_BASE}/themes/${themeId}`;
    return await apiRequest(url, {
      method: 'POST',
      body: JSON.stringify({
        theme_id: themeId,
        customizations,
        timestamp: new Date().toISOString()
      }),
    });
  } catch (error) {
    console.error('Failed to save theme customizations:', error);
    throw error;
  }
};

/**
 * Apply temporary preview changes without saving
 * @param {Object} customizations - The customization settings to preview
 * @returns {Promise<Object>} Preview operation result
 */
export const previewThemeCustomizations = async (customizations) => {
  try {
    const url = `${CUSTOMIZER_API_BASE}/preview`;
    return await apiRequest(url, {
      method: 'POST',
      body: JSON.stringify({
        customizations,
        preview: true,
        timestamp: new Date().toISOString()
      }),
    });
  } catch (error) {
    console.error('Failed to preview theme customizations:', error);
    throw error;
  }
};

/**
 * Upload logo file to WordPress Media Library
 * @param {File} file - The logo file to upload
 * @param {Function} onProgress - Progress callback function
 * @returns {Promise<Object>} Upload result with media URL and attachment ID
 */
export const uploadLogo = async (file, onProgress = null) => {
  try {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      throw new Error('Invalid file type. Please upload JPEG, PNG, GIF, or WebP images.');
    }

    // Validate file size (max 2MB)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
      throw new Error('File size exceeds 2MB limit. Please choose a smaller file.');
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('logo', file);
    formData.append('title', file.name);

    const url = `${CUSTOMIZER_API_BASE}/upload-logo`;

    // Create XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentComplete = (event.loaded / event.total) * 100;
            onProgress(percentComplete);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          reject(new Error(`Upload failed: ${xhr.statusText}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed due to network error'));
      });

      xhr.open('POST', url);

      // Add auth headers
      const authHeaders = getAuthHeaders();
      Object.entries(authHeaders).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });

      xhr.send(formData);
    });
  } catch (error) {
    console.error('Failed to upload logo:', error);
    throw error;
  }
};

/**
 * Iframe Communication Functions
 */

/**
 * Send customization updates to WordPress iframe for real-time preview
 * @param {HTMLIFrameElement} iframe - The WordPress iframe element
 * @param {Object} customizations - The customization settings to apply
 */
export const sendCustomizationToIframe = (iframe, customizations) => {
  if (!iframe || !iframe.contentWindow) {
    console.warn('Iframe not available for customization update');
    return;
  }

  try {
    const message = {
      type: 'THEME_CUSTOMIZATION_UPDATE',
      data: customizations,
      timestamp: Date.now(),
      source: 'nextjs-customizer'
    };

    iframe.contentWindow.postMessage(message, WP_BASE_URL);
  } catch (error) {
    console.error('Failed to send customization to iframe:', error);
  }
};

/**
 * Set up iframe message listener for communication from WordPress
 * @param {Function} onMessage - Callback function to handle messages from iframe
 * @returns {Function} Cleanup function to remove event listener
 */
export const setupIframeMessageListener = (onMessage) => {
  const messageHandler = (event) => {
    // Verify origin for security
    if (event.origin !== WP_BASE_URL) {
      return;
    }

    // Handle different message types
    if (event.data && event.data.type) {
      onMessage(event.data);
    }
  };

  window.addEventListener('message', messageHandler);

  // Return cleanup function
  return () => {
    window.removeEventListener('message', messageHandler);
  };
};

/**
 * Utility Functions
 */

/**
 * Validate customization data structure
 * @param {Object} customizations - The customization object to validate
 * @returns {boolean} Whether the customizations are valid
 */
export const validateCustomizations = (customizations) => {
  if (!customizations || typeof customizations !== 'object') {
    return false;
  }

  // Validate colors
  if (customizations.colors) {
    const { colors } = customizations;
    if (typeof colors !== 'object') return false;

    // Validate color format (hex colors)
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    for (const [key, value] of Object.entries(colors)) {
      if (typeof value === 'string' && !colorRegex.test(value)) {
        console.warn(`Invalid color format for ${key}: ${value}`);
        return false;
      }
    }
  }

  // Validate fonts
  if (customizations.fonts) {
    const { fonts } = customizations;
    if (typeof fonts !== 'object') return false;

    // Check required font properties
    if (fonts.primary && typeof fonts.primary !== 'string') return false;
    if (fonts.secondary && typeof fonts.secondary !== 'string') return false;
  }

  return true;
};

/**
 * Get WordPress base URL for iframe src
 * @returns {string} WordPress base URL
 */
export const getWordPressUrl = () => WP_BASE_URL;

export default {
  getThemeCustomizations,
  saveThemeCustomizations,
  previewThemeCustomizations,
  uploadLogo,
  sendCustomizationToIframe,
  setupIframeMessageListener,
  validateCustomizations,
  getWordPressUrl
};

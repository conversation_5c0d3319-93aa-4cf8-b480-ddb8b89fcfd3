/**
 * WordPress Content Service
 * Handles fetching and updating WordPress page content
 */

import { WordPressCredentials, getWordPressAuthHeaders } from './wordpress-auth';

export interface WordPressPage {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
    raw: string;
  };
  status: string;
  type: string;
  slug: string;
  link: string;
  date: string;
  modified: string;
}

export interface WordPressPageUpdate {
  title?: string;
  content?: string;
  status?: string;
}

export interface ContentOperationResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Fetch the home page content from WordPress (public access)
 */
export const fetchHomePageContentPublic = async (baseUrl: string): Promise<ContentOperationResult> => {
  try {
    // First, try to get the page with slug 'home'
    const response = await fetch(`${baseUrl}/wp-json/wp/v2/pages?slug=home&per_page=1`);

    if (!response.ok) {
      throw new Error(`Failed to fetch home page: ${response.status} ${response.statusText}`);
    }

    const pages: WordPressPage[] = await response.json();
    
    if (pages.length === 0) {
      // Try to get the front page
      const frontPageResponse = await fetch(`${baseUrl}/wp-json/wp/v2/pages?slug=front-page&per_page=1`);

      if (!frontPageResponse.ok) {
        throw new Error(`Failed to fetch front page: ${frontPageResponse.status} ${frontPageResponse.statusText}`);
      }

      const frontPages: WordPressPage[] = await frontPageResponse.json();
      
      if (frontPages.length === 0) {
        // Get the first published page as fallback
        const allPagesResponse = await fetch(`${baseUrl}/wp-json/wp/v2/pages?status=publish&per_page=1`);

        if (!allPagesResponse.ok) {
          throw new Error(`Failed to fetch any pages: ${allPagesResponse.status} ${allPagesResponse.statusText}`);
        }

        const allPages: WordPressPage[] = await allPagesResponse.json();
        
        if (allPages.length === 0) {
          throw new Error('No pages found on the WordPress site');
        }

        return {
          success: true,
          data: allPages[0],
        };
      }

      return {
        success: true,
        data: frontPages[0],
      };
    }

    return {
      success: true,
      data: pages[0],
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

/**
 * Fetch the home page content from WordPress
 */
export const fetchHomePageContent = async (credentials: WordPressCredentials): Promise<ContentOperationResult> => {
  try {
    // First, try to get the page with slug 'home' or 'front-page'
    const response = await fetch(`${credentials.baseUrl}/wp-json/wp/v2/pages?slug=home&per_page=1`, {
      method: 'GET',
      headers: getWordPressAuthHeaders(credentials),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch home page: ${response.status} ${response.statusText}`);
    }

    const pages: WordPressPage[] = await response.json();
    
    if (pages.length === 0) {
      // Try to get the front page
      const frontPageResponse = await fetch(`${credentials.baseUrl}/wp-json/wp/v2/pages?slug=front-page&per_page=1`, {
        method: 'GET',
        headers: getWordPressAuthHeaders(credentials),
      });

      if (!frontPageResponse.ok) {
        throw new Error(`Failed to fetch front page: ${frontPageResponse.status} ${frontPageResponse.statusText}`);
      }

      const frontPages: WordPressPage[] = await frontPageResponse.json();
      
      if (frontPages.length === 0) {
        // Get the first published page as fallback
        const allPagesResponse = await fetch(`${credentials.baseUrl}/wp-json/wp/v2/pages?status=publish&per_page=1`, {
          method: 'GET',
          headers: getWordPressAuthHeaders(credentials),
        });

        if (!allPagesResponse.ok) {
          throw new Error(`Failed to fetch any pages: ${allPagesResponse.status} ${allPagesResponse.statusText}`);
        }

        const allPages: WordPressPage[] = await allPagesResponse.json();
        
        if (allPages.length === 0) {
          throw new Error('No pages found on the WordPress site');
        }

        return {
          success: true,
          data: allPages[0],
        };
      }

      return {
        success: true,
        data: frontPages[0],
      };
    }

    return {
      success: true,
      data: pages[0],
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

/**
 * Update a WordPress page with new content
 */
export const updateWordPressPage = async (
  credentials: WordPressCredentials,
  pageId: number,
  updates: WordPressPageUpdate
): Promise<ContentOperationResult> => {
  try {
    const response = await fetch(`${credentials.baseUrl}/wp-json/wp/v2/pages/${pageId}`, {
      method: 'POST',
      headers: {
        ...getWordPressAuthHeaders(credentials),
        'X-HTTP-Method-Override': 'PUT',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `Failed to update page: ${response.status} ${response.statusText}`
      );
    }

    const updatedPage = await response.json();
    
    return {
      success: true,
      data: updatedPage,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

/**
 * Get all pages from WordPress site
 */
export const fetchAllPages = async (credentials: WordPressCredentials): Promise<ContentOperationResult> => {
  try {
    const response = await fetch(`${credentials.baseUrl}/wp-json/wp/v2/pages?per_page=100`, {
      method: 'GET',
      headers: getWordPressAuthHeaders(credentials),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch pages: ${response.status} ${response.statusText}`);
    }

    const pages: WordPressPage[] = await response.json();
    
    return {
      success: true,
      data: pages,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

/**
 * Get all pages from WordPress site (public access)
 */
export const fetchAllPagesPublic = async (baseUrl: string): Promise<ContentOperationResult> => {
  try {
    const response = await fetch(`${baseUrl}/wp-json/wp/v2/pages?per_page=100`);

    if (!response.ok) {
      throw new Error(`Failed to fetch pages: ${response.status} ${response.statusText}`);
    }

    const pages: WordPressPage[] = await response.json();
    
    return {
      success: true,
      data: pages,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}; 
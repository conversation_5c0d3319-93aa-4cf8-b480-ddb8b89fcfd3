import React, { useState } from 'react';

interface MapDomainModalProps {
  isOpen: boolean;
  onClose: () => void;
  siteName: string;
}

const MapDomainModal: React.FC<MapDomainModalProps> = ({ isOpen, onClose, siteName }) => {
  const [domainName, setDomainName] = useState<string>('');
  const [domainType, setDomainType] = useState<'Primary' | 'Alias'>('Primary');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
      <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Map Domain Name</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4">
          <label htmlFor="domainName" className="block text-sm font-medium text-gray-700">Enter Domain Name</label>
          <input
            type="text"
            id="domainName"
            className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="example.com"
            value={domainName}
            onChange={(e) => setDomainName(e.target.value)}
          />
        </div>

        <div className="mb-4">
          <div className="flex items-center space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio text-blue-600"
                name="domainType"
                value="Primary"
                checked={domainType === 'Primary'}
                onChange={() => setDomainType('Primary')}
              />
              <span className="ml-2">Primary</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio text-blue-600"
                name="domainType"
                value="Alias"
                checked={domainType === 'Alias'}
                onChange={() => setDomainType('Alias')}
              />
              <span className="ml-2">Alias</span>
            </label>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700">Point 'CNAME' records to</p>
          <div className="flex items-center p-3 mt-1 bg-gray-50 border border-gray-300 rounded-md">
            <span className="flex-1 text-sm text-gray-800 break-all">{siteName}</span>
            <button
              className="ml-2 text-gray-500 hover:text-gray-700"
              onClick={() => navigator.clipboard.writeText(siteName)}
              title="Copy to clipboard"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M17 16l-4 4-4-4" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 text-white bg-green-500 rounded-md hover:bg-green-600"
            onClick={() => {
              // Handle map domain logic here
              console.log('Mapping domain:', domainName, 'as', domainType);
              onClose();
            }}
          >
            Map Domain
          </button>
        </div>
      </div>
    </div>
  );
};

export default MapDomainModal; 
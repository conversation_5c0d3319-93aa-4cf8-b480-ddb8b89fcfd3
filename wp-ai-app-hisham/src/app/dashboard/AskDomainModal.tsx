'use client'
import React from 'react'

interface AskDomainModalProps {
  isOpen: boolean
  onYes: () => void
  onNo: () => void
}

const AskDomainModal: React.FC<AskDomainModalProps> = ({ isOpen, onYes, onNo }) => {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
      <div className="relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
        <h2 className="mb-2 text-xl font-semibold text-gray-800 text-center">
          Thank you for your purchase!
        </h2>
        <p className="mb-4 text-center text-gray-700">
          Do you already have a custom domain you’d like to use?
        </p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={onYes}
            className="px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700"
          >
            Yes
          </button>
          <button
            onClick={onNo}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
          >
            No
          </button>
        </div>
      </div>
    </div>
  )
}

export default AskDomainModal

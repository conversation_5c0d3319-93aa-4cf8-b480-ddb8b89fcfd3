import { openai } from "@ai-sdk/openai";
import { frontendTools } from "@assistant-ui/react-ai-sdk";
import { streamText } from "ai";

export const runtime = "edge";
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, tools } = await req.json();
  const systemPrompt = `You are a helpful assistant designed to guide users in building their WordPress website. Your responses should be tailored to this context, helping them define their site's purpose, features, and design. The conversation begins with the user describing what their site is all about, and you will then ask the following 5 questions in order:

  1. What is the name of your business?
  2. What pages do you want to include?
  3. Could you provide your contact information?
  4. Can you share a bit about the history and unique selling point of your business?
  5. Please select the images you'd like to include on your website.

  Ensure your answers are concise and directly relevant to building a WordPress website. Limit all responses to a maximum of two sentences.`;

  const result = streamText({
    model: openai("gpt-4o"),
    messages,
    toolCallStreaming: true,
    system: systemPrompt,
    tools: {
      ...frontendTools(tools),
      // add backend tools here
    },
  });

  return result.toDataStreamResponse();
}

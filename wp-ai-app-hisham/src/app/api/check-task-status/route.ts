import { NextResponse } from 'next/server';

interface TaskStatusResponse {
  status: boolean;
  message: string;
  data: {
    status: 'in progress' | 'completed' | 'failed';
    [key: string]: any;
  };
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('task_id');
    const instawpToken = searchParams.get('token');

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    if (!instawpToken) {
      return NextResponse.json(
        { error: 'InstaWP token is required' },
        { status: 400 }
      );
    }

    // Check task status via InstaWP API
    const statusResponse = await fetch(`https://app.instawp.io/api/v2/tasks/${taskId}/status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${instawpToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!statusResponse.ok) {
      throw new Error(`Failed to check task status: ${statusResponse.status} ${statusResponse.statusText}`);
    }

    const taskStatus: TaskStatusResponse = await statusResponse.json();

    return NextResponse.json({
      success: true,
      task_status: taskStatus.data.status,
      message: taskStatus.message,
      data: taskStatus.data
    });

  } catch (error) {
    console.error('Error checking task status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check task status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 
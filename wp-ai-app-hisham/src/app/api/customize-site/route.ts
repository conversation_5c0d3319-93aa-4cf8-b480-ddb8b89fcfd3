import { NextResponse } from "next/server";
import { pollTaskStatus } from "@/lib/task-status-checker";

interface CustomizationConfig {
  username: string;
  password: string;
  site_name: string;
  logo_url: string;
}

interface InstaWPSiteResponse {
  status: boolean;
  message: string;
  data: {
    task_id: string;
    status: number;
    wp_url: string;
    site_url?: string;
    [key: string]: any;
  };
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { instawp_token } = body;
    const site_name = body.site_name || "My Custom Site";

    console.log("Token:", instawp_token);

    // Step 1: Create new site via InstaWP API
    console.log("🚀 Step 1: Creating new site via InstaWP API...");
    const createSiteResponse = await fetch(
      "https://app.instawp.io/api/v2/sites/template",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${instawp_token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          // template_slug: 'spectra_1',
          template_slug: "frost",
          site_name: site_name,
          is_reserved: false,
          expiry_hours: 24
        }),
      }
    );

    console.log("createSiteResponse status:", createSiteResponse.status);
    const responseText = await createSiteResponse.text();
    console.log("createSiteResponse text:", responseText);

    if (!createSiteResponse.ok) {
      throw new Error("Failed to create site: " + responseText);
    }

    const siteData: InstaWPSiteResponse = JSON.parse(responseText);

    if (!siteData.status) {
      throw new Error(`Site creation failed: ${siteData.message}`);
    }

    const taskId = siteData.data.task_id;
    const newSiteUrl = siteData.data.wp_url || siteData.data.site_url;

    console.log("taskId", taskId);
    console.log("newSiteUrl", newSiteUrl);

    if (!taskId) {
      throw new Error("No task ID received from site creation");
    }

    console.log(`✅ Site creation initiated successfully`);
    console.log(`📋 Task ID: ${taskId}`);
    console.log(`🌐 Site URL: ${newSiteUrl}`);

    // Step 1.5: Verify task completion before proceeding
    console.log("⏳ Step 1.5: Verifying task completion...");
    try {
      // Get the base URL for the current request
      const requestUrl = new URL(request.url);
      const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;

      const taskStatus = await pollTaskStatus(taskId, instawp_token, {
        maxRetries: 30, // 30 retries
        retryDelay: 2000, // 2 seconds between retries
        timeout: 120000, // 2 minutes total timeout
        baseUrl: baseUrl, // Pass the correct base URL
      });

      console.log(`✅ Task ${taskId} completed successfully`);
      console.log(`📝 Task message: ${taskStatus.message}`);
    } catch (error) {
      console.error(`❌ Task ${taskId} failed or timed out:`, error);
      throw new Error(
        `Site installation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }

    const config: CustomizationConfig = {
      // username: 'sidobapera6161',
      username: "pamunorivu4561", //frost
      // password: 'C05s BytP VmYI cay4 udcu iYez',
      password: "dhR6 l2i5 ihHU EJ65 NyTl jDck", //frost
      site_name: site_name,
      logo_url: body.logo_url || "https://example.com/static-logo.jpg",
    };

    // Get color/font from body
    const backgroundColor = body.background_color || "#ffffff";
    const fontFamily = body.font_family || "Roboto";

    // Step 2: Fetch header template part
    console.log("🔧 Step 2: Fetching header template part...");
    const headerResponse = await fetch(
      `${newSiteUrl}/wp-json/wp/v2/template-parts?area=header`,
      {
        headers: {
          Authorization:
            "Basic " +
            Buffer.from(`${config.username}:${config.password}`).toString(
              "base64"
            ),
        },
      }
    );

    // Step 3: Fetch footer template part
    console.log("🔧 Step 3: Fetching footer template part...");
    const footerResponse = await fetch(
      `${newSiteUrl}/wp-json/wp/v2/template-parts?area=footer`,
      {
        headers: {
          Authorization:
            "Basic " +
            Buffer.from(`${config.username}:${config.password}`).toString(
              "base64"
            ),
        },
      }
    );

    console.log("headerResponse", headerResponse);
    console.log("footerResponse", footerResponse);

    if (!headerResponse.ok || !footerResponse.ok) {
      throw new Error("Failed to fetch template parts");
    }

    const [headerData] = await headerResponse.json();
    const [footerData] = await footerResponse.json();

    // Extract the PATCH URLs from the _links.self[0].href property
    const headerPatchUrl = headerData._links?.self?.[0]?.href;
    const footerPatchUrl = footerData._links?.self?.[0]?.href;

    if (!headerPatchUrl || !footerPatchUrl) {
      throw new Error(
        "Could not find patch URLs for header or footer template parts"
      );
    }

    // Step 4: Update header content (replace logo or title)
    console.log("🎨 Step 4: Updating header content...");
    let updatedHeaderContent;
    // Get the base URL for internal API calls
    const requestUrl = new URL(request.url);
    const baseUrl = `${requestUrl.protocol}//${requestUrl.host}`;
    const aiEndpoint = `${baseUrl}/api/ai-update-content`;
    try {
      console.log("Calling AI endpoint for header update with payload:", {
        rawContent: headerData.content.raw,
        config: { logo_url: config.logo_url, site_name: config.site_name },
        type: "header",
      });
      const aiRes = await fetch(
        aiEndpoint,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            rawContent: headerData.content.raw,
            config: { logo_url: config.logo_url, site_name: config.site_name },
            type: "header",
          }),
        }
      );
      console.log("AI endpoint status (header):", aiRes.status);
      let aiJson = null;
      try {
        aiJson = await aiRes.json();
        console.log("AI endpoint response (header):", aiJson);
      } catch (jsonErr) {
        console.error("Failed to parse AI endpoint response (header) as JSON:", jsonErr);
      }
      if (aiRes.ok && aiJson && aiJson.updatedContent) {
        updatedHeaderContent = aiJson.updatedContent;
      } else {
        console.error("AI endpoint failed for header. Status:", aiRes.status, "Response:", aiJson);
        throw new Error("AI endpoint failed for header");
      }
    } catch (e) {
      console.error("AI endpoint error (header):", e);
      // fallback to old logic if AI fails
      if (/<img\s/i.test(headerData.content.raw)) {
        updatedHeaderContent = headerData.content.raw.replace(
          /(src=["'])[^"']*?(["'])/,
          `$1${config.logo_url}$2`
        );
      } else {
        updatedHeaderContent = headerData.content.raw.replace(
          /(<h[1-6][^>]*>)(.*?)(<\/h[1-6]>)/i,
          `$1${config.site_name}$3`
        );
      }
    }

    // Step 5: Update footer content (replace site name)
    console.log("🎨 Step 5: Updating footer content...");
    let updatedFooterContent;
    try {
      console.log("Calling AI endpoint for footer update with payload:", {
        rawContent: footerData.content.raw,
        config: { site_name: config.site_name },
        type: "footer",
        year: new Date().getFullYear(),
      });
      const aiRes = await fetch(
        aiEndpoint,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            rawContent: footerData.content.raw,
            config: { site_name: config.site_name },
            type: "footer",
            year: new Date().getFullYear(),
          }),
        }
      );
      console.log("AI endpoint status (footer):", aiRes.status);
      let aiJson = null;
      try {
        aiJson = await aiRes.json();
        console.log("AI endpoint response (footer):", aiJson);
      } catch (jsonErr) {
        console.error("Failed to parse AI endpoint response (footer) as JSON:", jsonErr);
      }
      if (aiRes.ok && aiJson && aiJson.updatedContent) {
        updatedFooterContent = aiJson.updatedContent;
      } else {
        console.error("AI endpoint failed for footer. Status:", aiRes.status, "Response:", aiJson);
        throw new Error("AI endpoint failed for footer");
      }
    } catch (e) {
      console.error("AI endpoint error (footer):", e);
      // fallback to old logic if AI fails
      updatedFooterContent = footerData.content.raw.replace(
        /©\s*\d{4}\s*[^<]*/,
        `© ${new Date().getFullYear()} ${config.site_name}`
      );
    }

    // Step 6: Update header via PATCH request
    console.log("💾 Step 6: Saving header updates...");
    const updateHeaderResponse = await fetch(headerPatchUrl, {
      method: "PATCH",
      headers: {
        Authorization:
          "Basic " +
          Buffer.from(`${config.username}:${config.password}`).toString(
            "base64"
          ),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        content: {
          raw: updatedHeaderContent,
        },
      }),
    });

    // Step 7: Update footer via PATCH request
    console.log("💾 Step 7: Saving footer updates...");
    const updateFooterResponse = await fetch(footerPatchUrl, {
      method: "PATCH",
      headers: {
        Authorization:
          "Basic " +
          Buffer.from(`${config.username}:${config.password}`).toString(
            "base64"
          ),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        content: {
          raw: updatedFooterContent,
        },
      }),
    });

    if (!updateHeaderResponse.ok || !updateFooterResponse.ok) {
      throw new Error("Failed to update template parts");
    }

    // Step 8: Update global styles (background color & font family)
    console.log("🎨 Step 8: Updating global styles...");
    // 1. Fetch active theme to get global-styles endpoint
    const themeResponse = await fetch(
      `${newSiteUrl}/wp-json/wp/v2/themes?status=active`,
      {
        headers: {
          Authorization:
            "Basic " +
            Buffer.from(`${config.username}:${config.password}`).toString(
              "base64"
            ),
        },
      }
    );
    if (!themeResponse.ok) {
      throw new Error("Failed to fetch active theme");
    }
    const themeData = await themeResponse.json();
    // Robustly find the global styles endpoint
    let globalStylesHref = null;
    if (Array.isArray(themeData)) {
      for (const theme of themeData) {
        // Try direct key
        if (
          theme["wp:user-global-styles"] &&
          Array.isArray(theme["wp:user-global-styles"]) &&
          theme["wp:user-global-styles"][0]?.href
        ) {
          globalStylesHref = theme["wp:user-global-styles"][0].href;
          break;
        }
        // Try _links
        if (
          theme._links &&
          theme._links["wp:user-global-styles"] &&
          Array.isArray(theme._links["wp:user-global-styles"]) &&
          theme._links["wp:user-global-styles"][0]?.href
        ) {
          globalStylesHref = theme._links["wp:user-global-styles"][0].href;
          break;
        }
      }
    }
    if (!globalStylesHref) {
      console.error(
        "Could not find global styles endpoint. themeData:",
        JSON.stringify(themeData, null, 2)
      );
      throw new Error("Could not find global styles endpoint");
    }

    // 2. PATCH global styles with color/font
    const patchBody = {
      settings: {
        color: {
          palette: {
            theme: [
              {
                slug: "background",
                color: backgroundColor,
              },
            ],
          },
        },
        typography: {
          fontFamilies: {
            theme: [
              {
                fontFamily: `\"${fontFamily}\", sans-serif`,
                slug: "primary",
                name: "Primary",
              },
            ],
          },
        },
      },
      styles: {
        color: {
          background: "var(--wp--preset--color--background)",
        },
        typography: {
          fontFamily: "var(--wp--preset--font-family--primary)",
        },
      },
    };
    const globalStylesResponse = await fetch(globalStylesHref, {
      method: "PATCH",
      headers: {
        Authorization:
          "Basic " +
          Buffer.from(`${config.username}:${config.password}`).toString(
            "base64"
          ),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(patchBody),
    });
    if (!globalStylesResponse.ok) {
      throw new Error("Failed to update global styles");
    }
    console.log("✅ Global styles updated!");

    // === [NEW] Step: Homepage AI Content Generation ===
    if (body.description) {
      try {
        // 1. Fetch homepage by slug
        const homeRes = await fetch(`${newSiteUrl}/wp-json/wp/v2/pages?slug=home`, {
          headers: {
            Authorization:
              "Basic " +
              Buffer.from(`${config.username}:${config.password}`).toString("base64"),
          },
        });
        if (!homeRes.ok) throw new Error("Failed to fetch homepage");
        const homePages = await homeRes.json();
        const homePage = Array.isArray(homePages) ? homePages[0] : homePages;
        if (!homePage || !homePage.id) throw new Error("Homepage not found");
        const pageId = homePage.id;
        const currentHtml = homePage.content?.rendered || '';

        // 2. Call AI API to generate new homepage content
        const aiRes = await fetch(`${baseUrl}/api/ai-generate-homepage`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ description: body.description, currentHtml }),
        });
        if (!aiRes.ok) throw new Error("AI homepage generation failed");
        const aiJson = await aiRes.json();
        const updatedHtml = aiJson.updatedHtml;
        if (!updatedHtml) throw new Error("AI did not return updatedHtml");

        // 3. PATCH homepage with new content
        const patchRes = await fetch(`${newSiteUrl}/wp-json/wp/v2/pages/${pageId}`,
          {
            method: "PATCH",
            headers: {
              Authorization:
                "Basic " +
                Buffer.from(`${config.username}:${config.password}`).toString("base64"),
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              title: "Updated Home",
              content: updatedHtml,
            }),
          }
        );
        if (!patchRes.ok) throw new Error("Failed to update homepage");
        console.log("✅ Homepage updated with AI-generated content!");
      } catch (err) {
        console.error("❌ Homepage AI update failed:", err);
        // Optionally: continue without failing the whole process
      }
    }
    // === [END NEW] ===

    console.log("🎉 Site customization completed successfully!");

    return NextResponse.json({
      success: true,
      site_url: newSiteUrl,
      task_id: taskId,
      message: "Site created and customized successfully",
    });
  } catch (error) {
    console.error("❌ Error in site customization:", error);
    return NextResponse.json(
      {
        error: "Failed to customize site",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

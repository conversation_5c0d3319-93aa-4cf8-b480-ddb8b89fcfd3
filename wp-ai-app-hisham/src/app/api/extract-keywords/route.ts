import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const runtime = 'edge';

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Valid messages array is required' }, { status: 400 });
    }

    // Create a prompt for the AI to extract keywords for image search
    const prompt = `
      Based on the following conversation between a user and an assistant about creating a WordPress website,
      identify 3-5 specific keywords that would be most relevant for searching images on Pexels.
      Focus on the business type, industry, and visual elements that would make good website imagery.
      Return only the keywords as a JSON array of strings.

      Conversation:
      ${messages.map(m => `${m.role.toUpperCase()}: ${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('\n')}
    `;

    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3, // Lower temperature for more focused results
      response_format: { type: "json_object" }
    });

    // Extract the keywords from the AI response
    const content = response.choices[0].message?.content || '{"keywords":["business"]}';
    const parsedContent = JSON.parse(content);
    const keywords = Array.isArray(parsedContent.keywords) ? parsedContent.keywords : ["business"];

    return NextResponse.json({ keywords });
  } catch (error: any) {
    console.error('Error extracting image keywords:', error);
    return NextResponse.json({
      error: 'Failed to extract image keywords',
      keywords: ["business"]
    }, { status: 500 });
  }
}

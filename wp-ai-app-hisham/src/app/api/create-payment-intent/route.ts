import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-06-30.basil',
});

const PLANS = {
  starter: {
    name: 'Starter',
    price: 9,
    yearlyPrice: 7,
    description: 'Perfect for individuals and small projects',
  },
  pro: {
    name: 'Pro',
    price: 29,
    yearlyPrice: 24,
    description: 'Ideal for growing businesses and agencies',
  },
  turbo: {
    name: 'Turbo',
    price: 59,
    yearlyPrice: 49,
    description: 'For high-traffic sites and advanced teams',
  },
  enterprise: {
    name: 'Enterprise',
    price: 99,
    yearlyPrice: 79,
    description: 'For large organizations with advanced needs',
  },
};

export async function POST(req: NextRequest) {
  try {
    const { planId, isYearly } = await req.json();
    const plan = PLANS[planId as keyof typeof PLANS];
    
    if (!plan) {
      return NextResponse.json({ error: 'Invalid plan' }, { status: 400 });
    }

    const amount = (isYearly ? plan.yearlyPrice : plan.price) * 100; // Stripe expects cents
    
    // Create Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'aud',
            product_data: {
              name: `${plan.name} Plan`,
              description: `${plan.description}${isYearly ? ' (Yearly Billing)' : ' (Monthly Billing)'}`,
            },
            unit_amount: amount,
            recurring: isYearly ? {
              interval: 'year',
            } : {
              interval: 'month',
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription', // Use 'subscription' for recurring billing
      success_url: `${req.headers.origin}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.origin}/pricing`,
      metadata: {
        planId,
        isYearly: isYearly.toString(),
      },
      // Optional: Add customer email collection
      customer_email: undefined, // You can pass customer email here if available
      // Optional: Add tax calculation
      automatic_tax: { enabled: true },
    });

    return NextResponse.json({ 
      sessionId: session.id,
      url: session.url 
    });
  } catch (error: any) {
    console.error('Stripe error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
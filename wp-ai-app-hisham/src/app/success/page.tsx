"use client"
import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';

const SuccessPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [sessionData, setSessionData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (sessionId) {
      // Verify the session on the backend
      fetch('/api/verify-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId }),
      })
        .then(res => res.json())
        .then(data => {
          setSessionData(data);
          setLoading(false);
          // Redirect logic
          if (data && !data.error) {
            // Stripe status: 'complete' and payment_status: 'paid' means success
            if ((data.status === 'complete' || data.status === 'paid') || data.paymentStatus === 'paid') {
              setTimeout(() => router.replace('/dashboard'), 1500);
            } else {
              setTimeout(() => router.replace('/payments'), 1500);
            }
          } else {
            setTimeout(() => router.replace('/payments'), 1500);
          }
        })
        .catch(error => {
          setLoading(false);
          setTimeout(() => router.replace('/payments'), 1500);
        });
    }
  }, [sessionId, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Processing your payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-2xl mx-auto px-4 py-16">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-600 mb-8">
            <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          
          <h1 className="text-4xl font-bold mb-4">Payment Successful!</h1>
          <p className="text-xl text-gray-400 mb-8">
            Thank you for your purchase. Your subscription is now active.
          </p>
          
          {sessionData && (
            <div className="bg-gray-900 rounded-lg p-6 mb-8 text-left">
              <h2 className="text-lg font-semibold mb-4">Order Details</h2>
              <div className="space-y-2">
                <p><span className="text-gray-400">Plan:</span> {sessionData.planName}</p>
                <p><span className="text-gray-400">Amount:</span> ${sessionData.amount}</p>
                <p><span className="text-gray-400">Billing:</span> {sessionData.billing}</p>
                <p><span className="text-gray-400">Status:</span> {sessionData.status}</p>
              </div>
            </div>
          )}
          
          <div className="space-y-4">
            <Link 
              href="/dashboard" 
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Go to Dashboard
            </Link>
            <div>
              <Link 
                href="/pricing" 
                className="text-gray-400 hover:text-white transition-colors"
              >
                Back to Pricing
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessPage;
'use client';

import { useState } from 'react';

interface CustomizationResult {
  success: boolean;
  site_url?: string;
  task_id?: string;
  message?: string;
  error?: string;
  details?: string;
}

export default function SiteCustomizer() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<CustomizationResult | null>(null);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [formData, setFormData] = useState({
    instawp_token: '',
    site_name: '',
    logo_url: '',
    background_color: '#ffffff',
    font_family: 'Roboto',
    description: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult(null);
    setCurrentStep('Initializing site creation...');

    try {
      const response = await fetch('/api/customize-site', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data: CustomizationResult = await response.json();
      setResult(data);
      
      if (data.success) {
        setCurrentStep('Site customization completed successfully!');
      } else {
        setCurrentStep('Site customization failed');
      }
    } catch (error) {
      setResult({ 
        success: false, 
        error: 'Failed to customize site',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
      setCurrentStep('Site customization failed');
      console.error('Error customizing site:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="max-w-md p-6 mx-auto bg-white rounded-lg shadow-lg">
      <h1 className="mb-6 text-2xl font-bold">WordPress Site Customizer</h1>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="instawp_token" className="block text-sm font-medium text-gray-700">
            InstaWP Token
          </label>
          <input
            type="text"
            id="instawp_token"
            name="instawp_token"
            value={formData.instawp_token}
            onChange={handleChange}
            required
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <div>
          <label htmlFor="site_name" className="block text-sm font-medium text-gray-700">
            Site Name
          </label>
          <input
            type="text"
            id="site_name"
            name="site_name"
            value={formData.site_name}
            onChange={handleChange}
            placeholder="My Custom Site"
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <div>
          <label htmlFor="logo_url" className="block text-sm font-medium text-gray-700">
            Logo URL
          </label>
          <input
            type="url"
            id="logo_url"
            name="logo_url"
            value={formData.logo_url}
            onChange={handleChange}
            placeholder="https://example.com/logo.jpg"
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        {/* Background Color Picker */}
        <div>
          <label htmlFor="background_color" className="block text-sm font-medium text-gray-700">
            Background Color
          </label>
          <input
            type="color"
            id="background_color"
            name="background_color"
            value={formData.background_color}
            onChange={handleChange}
            className="block w-16 h-10 mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
          <input
            type="text"
            name="background_color"
            value={formData.background_color}
            onChange={handleChange}
            className="ml-2 w-28 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            pattern="#?[0-9A-Fa-f]{6}"
            maxLength={7}
            placeholder="#ffffff"
          />
        </div>

        {/* Font Family Dropdown */}
        <div>
          <label htmlFor="font_family" className="block text-sm font-medium text-gray-700">
            Font Family
          </label>
          <select
            id="font_family"
            name="font_family"
            value={formData.font_family}
            onChange={handleChange}
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          >
            <option value="Roboto">Roboto</option>
            <option value="Open Sans">Open Sans</option>
            <option value="Montserrat">Montserrat</option>
            <option value="Lato">Lato</option>
            <option value="Poppins">Poppins</option>
            <option value="Nunito">Nunito</option>
            <option value="Oswald">Oswald</option>
            <option value="Raleway">Raleway</option>
          </select>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Website Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="e.g. Landing page for a SaaS product"
            rows={3}
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            Describe the purpose or theme of your website. This will help generate homepage content.
          </p>
        </div>

        <button
          type="submit"
          disabled={loading}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? 'Creating and Customizing Site...' : 'Create & Customize Site'}
        </button>
      </form>

      {/* Loading Status */}
      {loading && currentStep && (
        <div className="mt-4 p-4 bg-blue-50 rounded-md">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <p className="text-blue-700">{currentStep}</p>
          </div>
          <p className="text-sm text-blue-600 mt-2">
            This process may take up to 2 minutes. Please wait...
          </p>
        </div>
      )}

      {/* Result Display */}
      {result && !loading && (
        <div className={`mt-4 p-4 rounded-md ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
          {result.success ? (
            <div>
              <div className="flex items-center mb-2">
                <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <p className="text-green-700 font-medium">Site created and customized successfully!</p>
              </div>
              {result.message && (
                <p className="text-green-600 text-sm mb-2">{result.message}</p>
              )}
              {result.task_id && (
                <p className="text-green-600 text-sm mb-2">Task ID: {result.task_id}</p>
              )}
              {result.site_url && (
                <a
                  href={result.site_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-green-600 underline hover:text-green-800"
                >
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                    <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                  </svg>
                  View your site
                </a>
              )}
            </div>
          ) : (
            <div>
              <div className="flex items-center mb-2">
                <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <p className="text-red-700 font-medium">{result.error || 'Site customization failed'}</p>
              </div>
              {result.details && (
                <p className="text-red-600 text-sm">{result.details}</p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
} 
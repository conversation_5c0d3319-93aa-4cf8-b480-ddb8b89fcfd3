"use client"
import React from "react";
import { useAuth } from '../../components/providers/AuthProvider';

const AccountPage: React.FC = () => {
  const { profile, loading } = useAuth();
  if (loading || !profile) {
    return <div className="max-w-2xl p-8 mx-auto mt-10 bg-white rounded-lg shadow-md">Loading...</div>;
  }
  const user = {
    name: `${profile.first_name} ${profile.last_name}`.trim(),
    email: profile.email,
    plan: 'Pro', // Placeholder, update if you add plan to profile
    joined: '', // Placeholder, update if you add joined date to profile
    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.first_name + ' ' + profile.last_name)}&background=0D8ABC&color=fff`
  };

  return (
    <div className="max-w-2xl p-8 mx-auto mt-10 bg-white rounded-lg shadow-md">
      <div className="flex items-center mb-8">
        <img
          src={user.avatar}
          alt="User Avatar"
          className="w-20 h-20 mr-6 border-4 border-blue-200 rounded-full shadow-sm"
        />
        <div>
          <h2 className="text-2xl font-bold text-gray-800">{user.name}</h2>
          <p className="text-gray-500">{user.email}</p>
          <span className="inline-block px-3 py-1 mt-2 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">
            {user.plan} Plan
          </span>
        </div>
      </div>
      <div className="mb-6">
        <h3 className="mb-2 text-lg font-semibold text-gray-700">Account Details</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <span className="block text-xs text-gray-500">Full Name</span>
            <span className="block font-medium text-gray-800">{user.name}</span>
          </div>
          <div>
            <span className="block text-xs text-gray-500">Email Address</span>
            <span className="block font-medium text-gray-800">{user.email}</span>
          </div>
          <div>
            <span className="block text-xs text-gray-500">Plan</span>
            <span className="block font-medium text-gray-800">{user.plan}</span>
          </div>
          <div>
            <span className="block text-xs text-gray-500">Member Since</span>
            <span className="block font-medium text-gray-800">{user.joined}</span>
          </div>
        </div>
      </div>
      <div className="mb-6">
        <h3 className="mb-2 text-lg font-semibold text-gray-700">Security</h3>
        <button className="px-4 py-2 text-white transition bg-blue-500 rounded-md hover:bg-blue-600">Change Password</button>
      </div>
      <div>
        <h3 className="mb-2 text-lg font-semibold text-gray-700">Danger Zone</h3>
        <button className="px-4 py-2 text-white transition bg-red-500 rounded-md hover:bg-red-600">Delete Account</button>
      </div>
    </div>
  );
};

export default AccountPage;

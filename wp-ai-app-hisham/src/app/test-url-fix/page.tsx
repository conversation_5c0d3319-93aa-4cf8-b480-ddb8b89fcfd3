'use client';

import { useState } from 'react';
import { pollTaskStatus, checkTaskStatusOnce } from '@/lib/task-status-checker';

export default function TestUrlFixPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [formData, setFormData] = useState({
    task_id: '',
    instawp_token: ''
  });

  const testUrlConstruction = async () => {
    setLoading(true);
    setResult('');

    try {
      // Test the URL construction without making actual API calls
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';
      const testUrl = `${baseUrl}/api/check-task-status?task_id=test&token=test`;
      
      setResult(`✅ URL construction test passed!\nBase URL: ${baseUrl}\nTest URL: ${testUrl}`);
    } catch (error) {
      setResult(`❌ URL construction test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testSingleCheck = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult('');

    try {
      const status = await checkTaskStatusOnce(formData.task_id, formData.instawp_token);
      setResult(`✅ Single check successful!\nStatus: ${status.task_status}\nMessage: ${status.message}`);
    } catch (error) {
      setResult(`❌ Single check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testPolling = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult('');

    try {
      // Use a shorter timeout for testing
      const status = await pollTaskStatus(formData.task_id, formData.instawp_token, {
        maxRetries: 3,
        retryDelay: 1000,
        timeout: 5000
      });
      setResult(`✅ Polling test successful!\nStatus: ${status.task_status}\nMessage: ${status.message}`);
    } catch (error) {
      setResult(`❌ Polling test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="max-w-2xl p-6 mx-auto bg-white rounded-lg shadow-lg">
      <h1 className="mb-6 text-2xl font-bold">URL Fix Test Page</h1>
      
      <div className="mb-6 p-4 bg-blue-50 rounded-md">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">Testing URL Construction Fix</h2>
        <p className="text-sm text-blue-700">
          This page tests the URL construction fix for the task status checker.
        </p>
      </div>

      {/* URL Construction Test */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">1. URL Construction Test</h3>
        <button
          onClick={testUrlConstruction}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Test URL Construction
        </button>
      </div>

      {/* Single Check Test */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">2. Single Status Check Test</h3>
        <form onSubmit={testSingleCheck} className="space-y-4">
          <div>
            <label htmlFor="task_id" className="block text-sm font-medium text-gray-700">
              Task ID
            </label>
            <input
              type="text"
              id="task_id"
              name="task_id"
              value={formData.task_id}
              onChange={handleChange}
              required
              placeholder="e.g., e63da48f043b"
              className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <div>
            <label htmlFor="instawp_token" className="block text-sm font-medium text-gray-700">
              InstaWP Token
            </label>
            <input
              type="text"
              id="instawp_token"
              name="instawp_token"
              value={formData.instawp_token}
              onChange={handleChange}
              required
              className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            Test Single Check
          </button>
        </form>
      </div>

      {/* Polling Test */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">3. Polling Test (Short Timeout)</h3>
        <form onSubmit={testPolling} className="space-y-4">
          <div>
            <label htmlFor="polling_task_id" className="block text-sm font-medium text-gray-700">
              Task ID
            </label>
            <input
              type="text"
              id="polling_task_id"
              name="task_id"
              value={formData.task_id}
              onChange={handleChange}
              required
              placeholder="e.g., e63da48f043b"
              className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <div>
            <label htmlFor="polling_instawp_token" className="block text-sm font-medium text-gray-700">
              InstaWP Token
            </label>
            <input
              type="text"
              id="polling_instawp_token"
              name="instawp_token"
              value={formData.instawp_token}
              onChange={handleChange}
              required
              className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
          >
            Test Polling (3 retries, 5s timeout)
          </button>
        </form>
      </div>

      {/* Results */}
      {result && (
        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Test Results</h3>
          <pre className="text-sm text-gray-700 whitespace-pre-wrap bg-white p-3 rounded border">
            {result}
          </pre>
        </div>
      )}

      {/* Environment Info */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-md">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">Environment Information</h3>
        <div className="text-sm text-yellow-700 space-y-1">
          <p><strong>Window Object:</strong> {typeof window !== 'undefined' ? 'Available' : 'Not Available'}</p>
          <p><strong>Base URL:</strong> {typeof window !== 'undefined' ? window.location.origin : 'Server-side'}</p>
          <p><strong>NEXT_PUBLIC_BASE_URL:</strong> {process.env.NEXT_PUBLIC_BASE_URL || 'Not Set'}</p>
          <p><strong>VERCEL_URL:</strong> {process.env.VERCEL_URL || 'Not Set'}</p>
        </div>
      </div>
    </div>
  );
} 
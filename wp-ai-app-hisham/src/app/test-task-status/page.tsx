'use client';

import { useState } from 'react';

interface TaskStatusResult {
  success: boolean;
  task_status?: 'in progress' | 'completed' | 'failed';
  message?: string;
  data?: any;
  error?: string;
  details?: string;
}

export default function TestTaskStatusPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<TaskStatusResult | null>(null);
  const [formData, setFormData] = useState({
    task_id: '',
    instawp_token: ''
  });

  const handleCheckStatus = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch(`/api/check-task-status?task_id=${formData.task_id}&token=${formData.instawp_token}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data: TaskStatusResult = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ 
        success: false, 
        error: 'Failed to check task status',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('Error checking task status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'in progress':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'in progress':
        return (
          <svg className="w-5 h-5 text-yellow-600 mr-2 animate-spin" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  return (
    <div className="max-w-2xl p-6 mx-auto bg-white rounded-lg shadow-lg">
      <h1 className="mb-6 text-2xl font-bold">Task Status Checker</h1>
      
      <div className="mb-6 p-4 bg-blue-50 rounded-md">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">How to use:</h2>
        <ol className="text-sm text-blue-700 space-y-1">
          <li>1. Get a task_id from a site creation response</li>
          <li>2. Enter your InstaWP token</li>
          <li>3. Click "Check Status" to verify task completion</li>
          <li>4. Only proceed with customization when status is "completed"</li>
        </ol>
      </div>
      
      <form onSubmit={handleCheckStatus} className="space-y-4">
        <div>
          <label htmlFor="task_id" className="block text-sm font-medium text-gray-700">
            Task ID
          </label>
          <input
            type="text"
            id="task_id"
            name="task_id"
            value={formData.task_id}
            onChange={handleChange}
            required
            placeholder="e.g., e63da48f043b"
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <div>
          <label htmlFor="instawp_token" className="block text-sm font-medium text-gray-700">
            InstaWP Token
          </label>
          <input
            type="text"
            id="instawp_token"
            name="instawp_token"
            value={formData.instawp_token}
            onChange={handleChange}
            required
            className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>

        <button
          type="submit"
          disabled={loading}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
            loading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {loading ? 'Checking Status...' : 'Check Status'}
        </button>
      </form>

      {/* Result Display */}
      {result && (
        <div className={`mt-6 p-4 rounded-md ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
          <div className="flex items-center mb-2">
            {getStatusIcon(result.task_status)}
            <p className={`font-medium ${getStatusColor(result.task_status)}`}>
              {result.success ? 'Status Check Successful' : 'Status Check Failed'}
            </p>
          </div>
          
          {result.success && (
            <div className="space-y-2">
              <p className="text-sm text-gray-700">
                <span className="font-medium">Task Status:</span> {result.task_status}
              </p>
              {result.message && (
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Message:</span> {result.message}
                </p>
              )}
              {result.data && (
                <div className="text-sm text-gray-700">
                  <span className="font-medium">Additional Data:</span>
                  <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}
          
          {!result.success && (
            <div>
              <p className="text-red-700">{result.error}</p>
              {result.details && (
                <p className="text-red-600 text-sm mt-1">{result.details}</p>
              )}
            </div>
          )}
        </div>
      )}

      {/* Example Response */}
      <div className="mt-6 p-4 bg-gray-50 rounded-md">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Example Site Creation Response:</h3>
        <pre className="text-xs text-gray-600 overflow-auto">
{`{
  "status": true,
  "message": "Site installation work is in progress, Please wait for installation.",
  "data": {
    "task_id": "e63da48f043b",
    "status": 1,
    "wp_url": "https://test5.instawp.co",
    ...
  }
}`}
        </pre>
      </div>
    </div>
  );
} 
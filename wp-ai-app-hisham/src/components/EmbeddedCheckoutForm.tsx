'use client';

import { useEffect, useState } from 'react';
import { EmbeddedCheckout, EmbeddedCheckoutProvider } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface EmbeddedCheckoutFormProps {
  clientSecret: string;
  onClose: () => void;
}

export default function EmbeddedCheckoutForm({ clientSecret, onClose }: EmbeddedCheckoutFormProps) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-full max-w-lg h-[90vh] md:max-w-2xl md:h-[90vh] bg-white rounded-lg shadow-xl flex flex-col overflow-hidden">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        <div className="p-0 flex-1 overflow-auto">
          {clientSecret && (
            <EmbeddedCheckoutProvider
              stripe={stripePromise}
              options={{ clientSecret }}
            >
              <div className="w-full h-full min-h-[500px]">
                <EmbeddedCheckout />
              </div>
            </EmbeddedCheckoutProvider>
          )}
        </div>
      </div>
    </div>
  );
}

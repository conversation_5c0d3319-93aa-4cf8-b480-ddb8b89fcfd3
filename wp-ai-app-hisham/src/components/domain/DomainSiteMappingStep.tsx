"use client";

import React, { useState, useEffect } from 'react';
import { Globe, CheckCircle, AlertCircle, ExternalLink, Clock } from 'lucide-react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

interface Site {
  id: string;
  site_name: string;
  expiry_status: 'Permanent' | 'Temporary';
  expiry_time?: string;
}

interface DomainSiteMappingStepProps {
  domainName: string;
  siteId?: string; // Pre-selected site ID if coming from checkout
  onComplete: (siteId: string) => void;
  onBack?: () => void;
}

const DomainSiteMappingStep: React.FC<DomainSiteMappingStepProps> = ({
  domainName,
  siteId: preSelectedSiteId,
  onComplete,
  onBack,
}) => {
  const [sites, setSites] = useState<Site[]>([]);
  const [selectedSiteId, setSelectedSiteId] = useState<string>(preSelectedSiteId || '');
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const supabase = createClientComponentClient();

  useEffect(() => {
    const fetchSites = async () => {
      try {
        const { data, error } = await supabase
          .from('user-websites')
          .select('id, site_name, expiry_status, expiry_time')
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        setSites(data as Site[]);
      } catch (err: any) {
        console.error('Error fetching sites:', err);
        setError('Failed to load your sites. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchSites();
  }, [supabase]);

  const handleSiteSelect = (siteId: string) => {
    setSelectedSiteId(siteId);
  };

  const handleComplete = async () => {
    if (!selectedSiteId) {
      setError('Please select a site to map your domain to.');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      // Call the completion handler
      await onComplete(selectedSiteId);
    } catch (err: any) {
      console.error('Error completing domain mapping:', err);
      setError(err.message || 'Failed to complete domain mapping. Please try again.');
      setProcessing(false);
    }
  };

  const getSiteStatusBadge = (site: Site) => {
    if (site.expiry_status === 'Permanent') {
      return (
        <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
          Permanent
        </span>
      );
    }
    return (
      <span className="px-2 py-1 bg-amber-100 text-amber-700 text-xs rounded-full font-medium">
        Temporary
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Clock className="w-8 h-8 text-gray-400 animate-spin mr-3" />
        <span className="text-gray-600">Loading your sites...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Map Your Domain</h2>
        <p className="text-gray-600">
          Choose which site you'd like to connect to <strong>{domainName}</strong>
        </p>
      </div>

      {/* Domain Info Card */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
          <div>
            <p className="font-medium text-green-800">Domain Successfully Registered!</p>
            <p className="text-sm text-green-600">
              {domainName} is now yours. Let's connect it to one of your sites.
            </p>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 font-medium">Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      )}

      {/* Sites List */}
      {sites.length === 0 ? (
        <div className="text-center py-8">
          <div className="bg-gray-50 rounded-lg p-6 max-w-md mx-auto">
            <Globe className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Sites Available</h3>
            <p className="text-gray-500 text-sm mb-4">
              You need to create a site first before you can map a domain to it.
            </p>
            <a
              href="/dashboard/create"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Create New Site
            </a>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Select a Site</h3>
          
          <div className="grid gap-3">
            {sites.map((site) => (
              <div
                key={site.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedSiteId === site.id
                    ? 'border-green-500 bg-green-50 ring-2 ring-green-200'
                    : 'border-gray-200 hover:border-green-300 hover:bg-green-50'
                }`}
                onClick={() => handleSiteSelect(site.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                      selectedSiteId === site.id
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300'
                    }`}>
                      {selectedSiteId === site.id && (
                        <div className="w-2 h-2 bg-white rounded-full" />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-lg font-medium text-gray-800 truncate">
                        {site.site_name}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        {getSiteStatusBadge(site)}
                        {site.expiry_time && site.expiry_status === 'Temporary' && (
                          <span className="text-xs text-gray-500">
                            Expires: {new Date(site.expiry_time).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {selectedSiteId === site.id && (
                    <CheckCircle className="w-6 h-6 text-green-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Information Box */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">What happens next?</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Your domain will be configured to point to the selected site</li>
          <li>• DNS settings will be automatically updated</li>
          <li>• Your site will be accessible via the new domain within a few minutes</li>
          <li>• You can change the mapping later from your domain management page</li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        {onBack && (
          <button
            onClick={onBack}
            disabled={processing}
            className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Back
          </button>
        )}
        
        <button
          onClick={handleComplete}
          disabled={processing || !selectedSiteId || sites.length === 0}
          className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {processing ? (
            <>
              <Clock className="w-5 h-5 animate-spin" />
              <span>Configuring...</span>
            </>
          ) : (
            <>
              <Globe className="w-5 h-5" />
              <span>Complete Domain Setup</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default DomainSiteMappingStep;

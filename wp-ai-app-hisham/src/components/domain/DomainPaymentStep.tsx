"use client";

import React, { useState } from 'react';
import { CreditCard, Shield, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../../components/providers/AuthProvider';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

interface DomainPaymentStepProps {
  selectedDomain: DomainCheckResult;
  onPaymentInitiated: () => void;
  onBack: () => void;
}

const DomainPaymentStep: React.FC<DomainPaymentStepProps> = ({
  selectedDomain,
  onPaymentInitiated,
  onBack,
}) => {
  const { user, session } = useAuth();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePayment = async () => {
    if (!selectedDomain.Price) {
      setError('Unable to determine domain price. Please go back and try again.');
      return;
    }

    if (!user || !session) {
      setError('You must be logged in to purchase a domain.');
      return;
    }

    setProcessing(true);
    setError(null);
    onPaymentInitiated();

    try {
      // Get the user's session token for authentication
      const token = session.access_token;

      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch('/api/domain-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          domain: selectedDomain.Domain,
          price: selectedDomain.Price,
          // Note: siteId will be selected in the next step after payment
          siteId: 'pending'
        }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to initiate payment.');
      }

      const { url } = await response.json();
      if (url) {
        // Redirect to Stripe Checkout
        window.location.href = url;
      } else {
        throw new Error('No Stripe Checkout URL returned.');
      }
    } catch (err: any) {
      console.error('Payment initiation error:', err);
      setError(err.message || 'Failed to initiate payment');
      setProcessing(false);
    }
  };

  const formatPrice = (price: number) => {
    return `AUD $${price.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Complete Your Purchase</h2>
        <p className="text-gray-600">
          Review your domain selection and proceed to secure payment.
        </p>
      </div>

      {/* Domain Summary Card */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Domain Summary</h3>
          <CheckCircle className="w-6 h-6 text-green-500" />
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Domain Name:</span>
            <span className="font-medium text-gray-800">{selectedDomain.Domain}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Registration Period:</span>
            <span className="font-medium text-gray-800">1 Year</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Type:</span>
            <span className="font-medium text-gray-800">
              {selectedDomain.IsPremiumName ? (
                <span className="px-2 py-1 bg-orange-100 text-orange-700 text-sm rounded-full">
                  Premium Domain
                </span>
              ) : (
                'Standard Domain'
              )}
            </span>
          </div>
          
          <hr className="border-gray-200" />
          
          <div className="flex justify-between items-center text-lg">
            <span className="font-semibold text-gray-800">Total:</span>
            <span className="font-bold text-green-600">
              {selectedDomain.Price ? formatPrice(selectedDomain.Price) : 'Price unavailable'}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Features */}
      <div className="grid md:grid-cols-3 gap-4">
        <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
          <Shield className="w-6 h-6 text-blue-600" />
          <div>
            <p className="font-medium text-blue-800">Secure Payment</p>
            <p className="text-sm text-blue-600">256-bit SSL encryption</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
          <Clock className="w-6 h-6 text-green-600" />
          <div>
            <p className="font-medium text-green-800">Instant Setup</p>
            <p className="text-sm text-green-600">Domain ready in minutes</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-lg">
          <CreditCard className="w-6 h-6 text-purple-600" />
          <div>
            <p className="font-medium text-purple-800">Easy Payment</p>
            <p className="text-sm text-purple-600">All major cards accepted</p>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 font-medium">Payment Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      )}

      {/* Important Notes */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <h4 className="font-medium text-amber-800 mb-2">Important Information</h4>
        <ul className="text-sm text-amber-700 space-y-1">
          <li>• Domain registration is for 1 year and will auto-renew unless cancelled</li>
          <li>• After payment, you'll be able to map this domain to one of your sites</li>
          <li>• DNS configuration will be handled automatically</li>
          <li>• Refunds are available within 30 days of registration</li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        <button
          onClick={onBack}
          disabled={processing}
          className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Back to Search
        </button>
        
        <button
          onClick={handlePayment}
          disabled={processing || !selectedDomain.Price}
          className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {processing ? (
            <>
              <Clock className="w-5 h-5 animate-spin" />
              <span>Processing...</span>
            </>
          ) : (
            <>
              <CreditCard className="w-5 h-5" />
              <span>Proceed to Payment</span>
            </>
          )}
        </button>
      </div>

      {/* Payment Provider Info */}
      <div className="text-center text-sm text-gray-500">
        <p>Payments are securely processed by Stripe</p>
      </div>
    </div>
  );
};

export default DomainPaymentStep;

# Migration Execution Guide: Step-by-Step Instructions

## Pre-Migration Checklist

### 1. Backup and Preparation
```bash
# Navigate to wp-ai-app directory
cd wp-ai-app

# Create backup branch
git checkout -b backup-pre-migration
git push origin backup-pre-migration

# Create migration branch
git checkout -b feature/migrate-auth-payment-dashboard
```

### 2. Verify Current State
```bash
# Ensure clean working directory
git status

# Verify package manager
which pnpm
pnpm --version

# Test current build
pnpm install
pnpm run build
```

## Phase 1: Dependency Installation

### 1.1 Install Core Dependencies
```bash
# Supabase dependencies
pnpm add @supabase/supabase-js@^2.50.3
pnpm add @supabase/auth-helpers-nextjs@^0.10.0

# Stripe dependencies
pnpm add @stripe/stripe-js@^7.4.0
pnpm add @stripe/react-stripe-js@^3.7.0
pnpm add stripe@^18.3.0

# Utility dependencies
pnpm add uuid@^11.1.0
```

### 1.2 Install UI Dependencies
```bash
# Radix UI components
pnpm add @radix-ui/react-tooltip@^1.2.6

# Material UI components
pnpm add @mui/material@^7.2.0
pnpm add @mui/icons-material@^7.2.0
pnpm add @emotion/react@^11.14.0
pnpm add @emotion/styled@^11.14.1

# Additional UI utilities
pnpm add react-tooltip@^5.29.1
pnpm add remark-gfm@^4.0.1
```

### 1.3 Install Development Dependencies
```bash
pnpm add -D @types/uuid@^10.0.0
```

### 1.4 Verify Installation
```bash
pnpm install
pnpm run build
```

## Phase 2: Environment Configuration

### 2.1 Create Environment File
```bash
# Create .env.local file
touch .env.local
```

### 2.2 Add Environment Variables
Copy the following to `.env.local`:
```env
# OpenAI Configuration (existing)
OPENAI_API_KEY=your_openai_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ermaaxnoyckezbjtegmq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVybWFheG5veWNrZXpianRlZ21xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMzI2NDgsImV4cCI6MjA2NjkwODY0OH0.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVybWFheG5veWNrZXpianRlZ21xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMzMjY0OCwiZXhwIjoyMDY2OTA4NjQ4fQ.EolDsGRfP--zGI63c7IodAQQ7xIKbN-Te3zP6mm3lUo

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RgxfeHKmibvltr1BzOUJe7sBIqAAhbuISagzDYhYMDNg8aBVCAd8nGXc3sD3C178U89van802LkVjizumG6ZOHo0053Ip2SNQ
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RgxfeHKmibvltr1v2p5lBLDggnTaxCrARZx6sg8kqzL8HByk80hKwYdWyXY4Hx4dYSsDXpZcnHi31Rq8BTnvGIl00DdDcvX4Z

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Domain Registration (Namecheap)
NAMECHEAP_API_USER=omarkk
NAMECHEAP_API_KEY=ec4a0a2c1bc54454959a8a92e0fa4ff5
NAMECHEAP_CLIENT_IP=*************
NAMECHEAP_USERNAME=omarkk
NAMECHEAP_SANDBOX=true

# Registrant Information
REGISTRANT_FIRST_NAME="John"
REGISTRANT_LAST_NAME="Doe"
REGISTRANT_ADDRESS1="123 Innovation Drive"
REGISTRANT_CITY="Techville"
REGISTRANT_STATE_PROVINCE="CA"
REGISTRANT_POSTAL_CODE="90210"
REGISTRANT_COUNTRY="US"
REGISTRANT_PHONE="+1.**********"
REGISTRANT_EMAIL="<EMAIL>"
```

## Phase 3: File Migration

### 3.1 Create Directory Structure
```bash
# Create necessary directories
mkdir -p src/components/providers
mkdir -p src/components/payment
mkdir -p src/components/dashboard/domain
mkdir -p src/app/login
mkdir -p src/app/signup
mkdir -p src/app/payments
mkdir -p src/app/dashboard/account
mkdir -p src/app/dashboard/billing
mkdir -p src/app/dashboard/domain/complete
mkdir -p src/app/api/stripe/customer
mkdir -p src/app/api/stripe/billing-data
mkdir -p src/types
mkdir -p src/hooks
mkdir -p migrations
```

### 3.2 Copy Core Authentication Files
```bash
# Copy from wp-ai-app-hisham to wp-ai-app
cp ../wp-ai-app-hisham/src/components/providers/AuthProvider.tsx src/components/providers/
cp ../wp-ai-app-hisham/src/app/login/page.tsx src/app/login/
cp ../wp-ai-app-hisham/src/app/signup/page.tsx src/app/signup/
cp ../wp-ai-app-hisham/src/lib/wordpress-auth.ts src/lib/
```

### 3.3 Copy Payment System Files
```bash
# Stripe configuration
cp ../wp-ai-app-hisham/src/lib/stripe.ts src/lib/
cp ../wp-ai-app-hisham/src/lib/stripe-plans.ts src/lib/
cp ../wp-ai-app-hisham/src/lib/stripe-customer.ts src/lib/

# Payment pages and components
cp ../wp-ai-app-hisham/src/app/payments/page.tsx src/app/payments/
cp ../wp-ai-app-hisham/src/components/StripeModal.tsx src/components/payment/
cp ../wp-ai-app-hisham/src/app/EmbeddedCheckoutForm.tsx src/components/payment/

# Payment API endpoints
cp -r ../wp-ai-app-hisham/src/app/api/create-checkout-session src/app/api/
cp -r ../wp-ai-app-hisham/src/app/api/create-payment-intent src/app/api/
cp -r ../wp-ai-app-hisham/src/app/api/verify-session src/app/api/
cp -r ../wp-ai-app-hisham/src/app/api/domain-checkout-session src/app/api/
cp -r ../wp-ai-app-hisham/src/app/api/stripe src/app/api/
```

### 3.4 Copy Dashboard System Files
```bash
# Dashboard layout and pages
cp ../wp-ai-app-hisham/src/app/dashboard/layout.tsx src/app/dashboard/
cp ../wp-ai-app-hisham/src/app/dashboard/page.tsx src/app/dashboard/
cp ../wp-ai-app-hisham/src/app/dashboard/account/page.tsx src/app/dashboard/account/
cp ../wp-ai-app-hisham/src/app/dashboard/billing/page.tsx src/app/dashboard/billing/
cp -r ../wp-ai-app-hisham/src/app/dashboard/domain src/app/dashboard/

# Dashboard components
cp -r ../wp-ai-app-hisham/src/components/domain src/components/dashboard/
```

### 3.5 Copy Configuration and Utility Files
```bash
# Types and configuration
cp ../wp-ai-app-hisham/supabase-types.ts src/types/supabase.ts
cp ../wp-ai-app-hisham/components.json ./

# Database migrations
cp -r ../wp-ai-app-hisham/migrations ./

# Hooks and utilities
cp ../wp-ai-app-hisham/src/hooks/useDomainSearch.ts src/hooks/
cp ../wp-ai-app-hisham/src/lib/task-status-checker.ts src/lib/
```

## Phase 4: Integration and Updates

### 4.1 Update Root Layout
Edit `src/app/layout.tsx` to include AuthProvider:

```typescript
import { AuthProvider } from '@/components/providers/AuthProvider'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
```

### 4.2 Update Import Paths
Review and update any import paths that may need adjustment for the new structure.

### 4.3 Fix Component References
Update any component references to match the new file structure.

## Phase 5: Testing and Verification

### 5.1 Build Test
```bash
pnpm run build
```

### 5.2 Development Server Test
```bash
pnpm run dev
```

### 5.3 Manual Testing Checklist
- [ ] Navigate to `/login` - page loads correctly
- [ ] Navigate to `/signup` - page loads correctly
- [ ] Navigate to `/payments` - page loads correctly
- [ ] Navigate to `/dashboard` - page loads correctly (may require auth)
- [ ] Test authentication flow
- [ ] Test payment flow (in test mode)
- [ ] Test dashboard navigation

## Phase 6: Troubleshooting Common Issues

### 6.1 Import Errors
```bash
# If you see import errors, check:
# 1. File paths are correct
# 2. Dependencies are installed
# 3. TypeScript types are available
```

### 6.2 Environment Variable Issues
```bash
# Verify environment variables are loaded
# Check .env.local file exists and has correct values
# Restart development server after changes
```

### 6.3 Build Errors
```bash
# Common fixes:
# 1. Clear Next.js cache: rm -rf .next
# 2. Reinstall dependencies: rm -rf node_modules && pnpm install
# 3. Check TypeScript errors: pnpm run type-check
```

## Phase 7: Final Verification

### 7.1 Complete Feature Test
- [ ] User registration works
- [ ] User login works
- [ ] Payment processing works (test mode)
- [ ] Dashboard displays correctly
- [ ] All navigation works
- [ ] No console errors

### 7.2 Integration Test
- [ ] Existing site builder still works
- [ ] No conflicts with existing features
- [ ] Performance is acceptable

### 7.3 Commit Changes
```bash
git add .
git commit -m "feat: migrate authentication, payment, and dashboard systems

- Add Supabase authentication with AuthProvider
- Add Stripe payment processing and subscription management
- Add comprehensive dashboard with user management
- Add domain registration and management
- Integrate with existing site builder functionality"

git push origin feature/migrate-auth-payment-dashboard
```

## Post-Migration Tasks

### 1. Create Pull Request
- Document all changes made
- Include testing instructions
- Request code review

### 2. Update Documentation
- Update README.md with new features
- Document environment variable requirements
- Create user guides for new functionality

### 3. Production Deployment Preparation
- Update production environment variables
- Configure Stripe webhooks
- Set up Supabase production database
- Test in staging environment

## Rollback Plan

If issues arise during migration:

```bash
# Switch back to backup branch
git checkout backup-pre-migration

# Or reset to previous state
git reset --hard HEAD~1
```

## Support and Resources

- **Supabase Documentation**: https://supabase.com/docs
- **Stripe Documentation**: https://stripe.com/docs
- **Next.js Documentation**: https://nextjs.org/docs
- **Migration Plan**: See MIGRATION_PLAN.md
- **File Mapping**: See FILE_MAPPING.md

## Estimated Timeline

- **Phase 1-2**: 2-3 hours (Dependencies and Environment)
- **Phase 3**: 4-6 hours (File Migration)
- **Phase 4**: 2-3 hours (Integration)
- **Phase 5-6**: 2-4 hours (Testing and Fixes)
- **Phase 7**: 1-2 hours (Verification and Commit)

**Total Estimated Time**: 11-18 hours

This guide provides step-by-step instructions for executing the migration plan. Follow each phase carefully and test thoroughly at each step.

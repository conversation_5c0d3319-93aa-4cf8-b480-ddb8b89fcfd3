# File Migration Mapping: wp-ai-app-hisham → wp-ai-app

## Authentication System Files

### Core Authentication Components
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/components/providers/AuthProvider.tsx` | `src/components/providers/AuthProvider.tsx` | HIGH | Main auth context with Supabase |
| `src/app/login/page.tsx` | `src/app/login/page.tsx` | HIGH | Login page with OAuth |
| `src/app/signup/page.tsx` | `src/app/signup/page.tsx` | HIGH | Registration page |
| `src/app/components/EyeIcons.tsx` | `src/components/ui/EyeIcons.tsx` | MEDIUM | Password visibility icons |

### Authentication Utilities
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/lib/wordpress-auth.ts` | `src/lib/wordpress-auth.ts` | MEDIUM | WordPress API auth utilities |

## Payment System Files

### Stripe Configuration
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/lib/stripe.ts` | `src/lib/stripe.ts` | HIGH | Main Stripe configuration |
| `src/lib/stripe-plans.ts` | `src/lib/stripe-plans.ts` | HIGH | Plan definitions and pricing |
| `src/lib/stripe-customer.ts` | `src/lib/stripe-customer.ts` | HIGH | Customer management utilities |

### Payment Pages and Components
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/app/payments/page.tsx` | `src/app/payments/page.tsx` | HIGH | Main payment page |
| `src/components/StripeModal.tsx` | `src/components/payment/StripeModal.tsx` | HIGH | Payment modal component |
| `src/app/EmbeddedCheckoutForm.tsx` | `src/components/payment/EmbeddedCheckoutForm.tsx` | MEDIUM | Embedded checkout |
| `src/app/return/page.tsx` | `src/app/return/page.tsx` | MEDIUM | Payment return page |
| `src/app/success/page.tsx` | `src/app/success/page.tsx` | MEDIUM | Payment success page |

### Payment API Endpoints
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/app/api/create-checkout-session/route.ts` | `src/app/api/create-checkout-session/route.ts` | HIGH | Subscription checkout |
| `src/app/api/create-payment-intent/route.ts` | `src/app/api/create-payment-intent/route.ts` | HIGH | Payment intent creation |
| `src/app/api/verify-session/route.ts` | `src/app/api/verify-session/route.ts` | HIGH | Payment verification |
| `src/app/api/embedded-checkout/route.ts` | `src/app/api/embedded-checkout/route.ts` | MEDIUM | Embedded checkout API |
| `src/app/api/domain-checkout-session/route.ts` | `src/app/api/domain-checkout-session/route.ts` | HIGH | Domain payment checkout |
| `src/app/api/stripe/customer/route.ts` | `src/app/api/stripe/customer/route.ts` | HIGH | Customer management API |
| `src/app/api/stripe/billing-data/route.ts` | `src/app/api/stripe/billing-data/route.ts` | HIGH | Billing data API |

## Dashboard System Files

### Dashboard Layout and Navigation
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/app/dashboard/layout.tsx` | `src/app/dashboard/layout.tsx` | HIGH | Main dashboard layout |

### Dashboard Pages
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/app/dashboard/page.tsx` | `src/app/dashboard/page.tsx` | HIGH | Main dashboard (websites) |
| `src/app/dashboard/account/page.tsx` | `src/app/dashboard/account/page.tsx` | HIGH | User profile management |
| `src/app/dashboard/billing/page.tsx` | `src/app/dashboard/billing/page.tsx` | HIGH | Billing management |
| `src/app/dashboard/domain/page.tsx` | `src/app/dashboard/domain/page.tsx` | HIGH | Domain management |
| `src/app/dashboard/domain/complete/page.tsx` | `src/app/dashboard/domain/complete/page.tsx` | HIGH | Domain completion |
| `src/app/dashboard/create/page.tsx` | `src/app/dashboard/create/page.tsx` | MEDIUM | Site creation page |

### Dashboard Components
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/components/domain/DomainSearchStep.tsx` | `src/components/dashboard/domain/DomainSearchStep.tsx` | HIGH | Domain search component |
| `src/components/domain/DomainPaymentStep.tsx` | `src/components/dashboard/domain/DomainPaymentStep.tsx` | HIGH | Domain payment component |
| `src/components/domain/MyDomainsSection.tsx` | `src/components/dashboard/domain/MyDomainsSection.tsx` | HIGH | Domain listing component |

## Utility and Configuration Files

### Database and Types
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `supabase-types.ts` | `src/types/supabase.ts` | HIGH | Supabase type definitions |
| `migrations/create_domains_table.sql` | `migrations/create_domains_table.sql` | HIGH | Database schema |

### Hooks and Utilities
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/hooks/useDomainSearch.ts` | `src/hooks/useDomainSearch.ts` | MEDIUM | Domain search hook |
| `src/lib/task-status-checker.ts` | `src/lib/task-status-checker.ts` | MEDIUM | Task status utilities |

### API Utilities
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `src/app/api/customize-site/route.ts` | `src/app/api/customize-site/route.ts` | MEDIUM | Site customization API |
| `src/app/api/ai-generate-homepage/route.ts` | `src/app/api/ai-generate-homepage/route.ts` | LOW | AI homepage generation |

## Environment and Configuration

### Environment Variables
| Variable | Required | Notes |
|----------|----------|-------|
| `NEXT_PUBLIC_SUPABASE_URL` | YES | Supabase project URL |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | YES | Supabase anonymous key |
| `SUPABASE_SERVICE_ROLE_KEY` | YES | Supabase service role key |
| `STRIPE_SECRET_KEY` | YES | Stripe secret key |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | YES | Stripe publishable key |
| `NEXT_PUBLIC_BASE_URL` | YES | Application base URL |
| `NAMECHEAP_API_USER` | YES | Namecheap API user |
| `NAMECHEAP_API_KEY` | YES | Namecheap API key |
| `NAMECHEAP_CLIENT_IP` | YES | Namecheap client IP |
| `NAMECHEAP_USERNAME` | YES | Namecheap username |
| `NAMECHEAP_SANDBOX` | YES | Namecheap sandbox mode |

### Configuration Files
| Source File | Target File | Priority | Notes |
|-------------|-------------|----------|-------|
| `.env.local` | `.env.local` | HIGH | Environment variables |
| `components.json` | `components.json` | MEDIUM | UI components config |

## Dependencies to Add

### Core Dependencies
```json
{
  "@supabase/supabase-js": "^2.50.3",
  "@supabase/auth-helpers-nextjs": "^0.10.0",
  "@stripe/stripe-js": "^7.4.0",
  "@stripe/react-stripe-js": "^3.7.0",
  "stripe": "^18.3.0",
  "uuid": "^11.1.0"
}
```

### UI Dependencies
```json
{
  "@radix-ui/react-tooltip": "^1.2.6",
  "@mui/material": "^7.2.0",
  "@mui/icons-material": "^7.2.0",
  "@emotion/react": "^11.14.0",
  "@emotion/styled": "^11.14.1",
  "react-tooltip": "^5.29.1"
}
```

### Development Dependencies
```json
{
  "@types/uuid": "^10.0.0"
}
```

## Migration Priority Levels

### HIGH Priority (Critical for basic functionality)
- Authentication system core components
- Payment processing and Stripe integration
- Dashboard layout and main pages
- Database schema and types
- Environment configuration

### MEDIUM Priority (Important for full functionality)
- Domain management components
- Additional dashboard pages
- Payment return/success pages
- Utility hooks and functions

### LOW Priority (Nice to have)
- AI generation endpoints
- Additional utility functions
- Documentation files

## Potential Conflicts

### File Conflicts
- No direct file conflicts identified
- Target repository has clean structure for migration

### Component Conflicts
- Existing `Pricing.tsx` component may need integration
- Layout components may need coordination

### Route Conflicts
- No existing `/dashboard`, `/login`, `/signup` routes in target
- Clean migration path for all routes

## Integration Points

### With Existing Site Builder
- Dashboard should integrate with existing site creation flow
- Payment system should work with existing template selection
- Authentication should protect existing builder routes

### With Existing Components
- Integrate payment functionality with existing pricing components
- Ensure consistent styling with existing UI components
- Maintain existing navigation patterns where possible

## Testing Requirements

### Authentication Testing
- Login/logout functionality
- OAuth provider integration
- Session persistence
- Profile management

### Payment Testing
- Stripe checkout process
- Subscription management
- Domain payment processing
- Billing history display

### Dashboard Testing
- All page navigation
- Responsive design
- User data management
- Integration with site builder

## Success Metrics

- All migrated pages load without errors
- Authentication flow works end-to-end
- Payment processing completes successfully
- Dashboard functionality is fully operational
- No conflicts with existing features
- Performance is maintained or improved

# DNS Management Feature

## Overview

The DNS Management feature provides a comprehensive interface for managing DNS records for registered domains. Users can view, add, edit, and delete DNS records through an intuitive web interface.

## Features

### 1. DNS Records Management
- **View DNS Records**: Display all DNS records for a domain in a table format
- **Add DNS Records**: Create new DNS records with validation
- **Edit DNS Records**: Modify existing DNS records inline
- **Delete DNS Records**: Remove DNS records with confirmation
- **Copy Values**: Quick copy functionality for record names and values

### 2. Supported DNS Record Types
- **A Records**: IPv4 address records
- **AAAA Records**: IPv6 address records
- **CNAME Records**: Canonical name records
- **MX Records**: Mail exchange records (with priority)
- **TXT Records**: Text records
- **NS Records**: Name server records
- **PTR Records**: Pointer records
- **SRV Records**: Service records
- **CAA Records**: Certificate authority records

### 3. Validation & Security
- **Input Validation**: Comprehensive validation for all DNS record types
- **User Authorization**: Users can only manage DNS records for their own domains
- **Row Level Security**: Database-level security policies
- **Error Handling**: Proper error messages and user feedback

## File Structure

```
src/
├── app/
│   └── dashboard/
│       └── domain/
│           └── [domainId]/
│               └── dns/
│                   └── page.tsx                    # DNS management page
├── components/
│   └── dashboard/
│       └── domain/
│           ├── DNSRecordsTable.tsx                 # DNS records table component
│           ├── AddDNSRecordModal.tsx               # Add DNS record modal
│           ├── EditDNSRecordModal.tsx              # Edit DNS record modal
│           └── DeleteDNSRecordModal.tsx            # Delete DNS record modal
└── app/
    └── api/
        └── domains/
            ├── [domainId]/
            │   ├── route.ts                        # Single domain API
            │   └── dns/
            │       ├── route.ts                    # DNS records CRUD API
            │       └── [recordId]/
            │           └── route.ts                # Individual DNS record API
```

## API Endpoints

### Domain Management
- `GET /api/domains/[domainId]` - Fetch a single domain
- `GET /api/domains` - Fetch all domains for user

### DNS Records Management
- `GET /api/domains/[domainId]/dns` - Fetch DNS records for a domain
- `POST /api/domains/[domainId]/dns` - Create a new DNS record
- `PUT /api/domains/[domainId]/dns/[recordId]` - Update a DNS record
- `DELETE /api/domains/[domainId]/dns/[recordId]` - Delete a DNS record

## Database Schema

### DNS Records Table
```sql
CREATE TABLE dns_records (
    id UUID PRIMARY KEY,
    domain_id UUID NOT NULL REFERENCES domains(id),
    type VARCHAR(10) NOT NULL,
    name VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    ttl INTEGER NOT NULL DEFAULT 3600,
    priority INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Usage

### Accessing DNS Management
1. Navigate to the Domains page (`/dashboard/domain`)
2. Click the "Manage" button on any domain card
3. This will navigate to `/dashboard/domain/[domainId]/dns`

### Adding DNS Records
1. Click the "Add Record" button
2. Select the DNS record type
3. Enter the name (leave empty for root domain)
4. Enter the value according to the record type
5. Set TTL (Time To Live)
6. For MX records, set the priority
7. Click "Add Record"

### Editing DNS Records
1. Click the edit icon (pencil) next to any DNS record
2. Modify the record details
3. Click "Update Record"

### Deleting DNS Records
1. Click the delete icon (trash) next to any DNS record
2. Type "DELETE" to confirm
3. Click "Delete Record"

## Validation Rules

### A Records
- Value must be a valid IPv4 address format
- Example: `***********`

### AAAA Records
- Value must be a valid IPv6 address format
- Example: `2001:db8::1`

### CNAME Records
- Value must be a valid domain name format
- Example: `example.com`

### MX Records
- Value must be a valid domain name format
- Priority is required (0-65535)
- Example: `mail.example.com`

### TXT Records
- Value cannot exceed 255 characters
- Example: `"v=spf1 include:_spf.google.com ~all"`

### TTL Validation
- Must be between 60 seconds and 1 week (604800 seconds)
- Common values: 300 (5 min), 3600 (1 hour), 86400 (1 day)

## Security Features

### Row Level Security (RLS)
- Users can only access DNS records for their own domains
- Database-level security policies prevent unauthorized access

### Input Validation
- Server-side validation for all inputs
- Type-specific validation rules
- XSS protection through proper escaping

### Authentication
- JWT-based authentication required for all API calls
- User ownership verification for all operations

## Error Handling

### User-Friendly Error Messages
- Clear error messages for validation failures
- Network error handling with retry options
- Loading states and progress indicators

### Error Types
- **Validation Errors**: Invalid input format or values
- **Authorization Errors**: Unauthorized access attempts
- **Network Errors**: Connection or server issues
- **Not Found Errors**: Domain or record not found

## UI/UX Features

### Responsive Design
- Mobile-friendly interface
- Responsive table layout
- Touch-friendly buttons and controls

### Visual Feedback
- Loading spinners during operations
- Success/error notifications
- Status indicators for DNS records
- Color-coded record types

### Accessibility
- Keyboard navigation support
- Screen reader friendly
- High contrast mode support
- Focus indicators

## Performance Considerations

### Database Optimization
- Indexed columns for fast queries
- Efficient joins for domain lookups
- Pagination support for large record sets

### Frontend Optimization
- Lazy loading of components
- Optimistic updates for better UX
- Debounced search and filtering
- Efficient state management

## Future Enhancements

### Planned Features
- **Bulk Operations**: Add/edit/delete multiple records at once
- **DNS Propagation Check**: Verify if records are propagated
- **DNS Health Monitoring**: Monitor record status and health
- **Import/Export**: Import DNS records from other providers
- **DNS Templates**: Pre-configured DNS record templates
- **Advanced Validation**: More sophisticated DNS record validation
- **DNS Analytics**: Track DNS record changes and usage

### Technical Improvements
- **Real-time Updates**: WebSocket support for live updates
- **Caching**: Redis caching for frequently accessed records
- **Rate Limiting**: API rate limiting for abuse prevention
- **Audit Logging**: Track all DNS record changes
- **Backup/Restore**: DNS record backup and restore functionality

## Troubleshooting

### Common Issues

#### DNS Record Not Saving
- Check if the domain belongs to the user
- Verify all required fields are filled
- Ensure proper validation rules are met

#### API Errors
- Verify authentication token is valid
- Check network connectivity
- Review server logs for detailed error messages

#### Database Issues
- Ensure DNS records table exists
- Verify RLS policies are properly configured
- Check database connection and permissions

## Deployment Notes

### Database Migration
1. Run the SQL migration script to create the `dns_records` table
2. Verify RLS policies are properly applied
3. Test user access and permissions

### Environment Variables
- Ensure Supabase connection is configured
- Verify authentication is properly set up
- Test API endpoints in development environment

### Monitoring
- Monitor API response times
- Track DNS record creation/deletion rates
- Monitor error rates and user feedback 
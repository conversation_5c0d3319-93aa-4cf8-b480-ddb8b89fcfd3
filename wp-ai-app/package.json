{"name": "wordpress-ai-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@assistant-ui/react": "^0.10.29", "@assistant-ui/react-ai-sdk": "^0.10.16", "@assistant-ui/react-markdown": "^0.10.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.8.0", "@stripe/stripe-js": "^7.6.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.52.1", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "framer-motion": "^12.19.1", "lucide-react": "^0.503.0", "next": "14.1.0", "openai": "^4.97.0", "react": "18.2.0", "react-colorful": "^5.6.1", "react-dom": "18.2.0", "react-icons": "^5.5.0", "react-tooltip": "^5.29.1", "remark-gfm": "^4.0.1", "stripe": "^18.3.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "webfontloader": "^1.6.28"}, "devDependencies": {"@types/dompurify": "^3.2.0", "@types/react": "19.1.2", "@types/uuid": "^10.0.0", "@types/webfontloader": "^1.6.38", "autoprefixer": "^10.4.14", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.24", "tailwindcss": "^3.4.1"}, "packageManager": "pnpm@10.6.4+sha512.da3d715bfd22a9a105e6e8088cfc7826699332ded60c423b14ec613a185f1602206702ff0fe4c438cb15c979081ce4cb02568e364b15174503a63c7a8e2a5f6c"}
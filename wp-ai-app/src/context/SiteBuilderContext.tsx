'use client';

import React, { createContext, useState, useContext } from 'react';

// Define the type for the context value
type WebsiteFormData = {
  step: number;
  name: string;
  category: string;
  description: string;
  contact: {
    email: string;
    phone: string;
    address: string;
    socialMedia: Record<string, string>;
  };
  images: Array<{
    id: string;
    url: string;
    isAIGenerated: boolean;
  }>;
  uploadedImages: string[];  
  selectedImages: string[];
  selectedTemplate: number | null;
  selectedSitePath?: string | null;
  previewKey?: string | null;
  imageDimensions?: Record<string, { width: number; height: number }>;
  customization: {
    fontFamily: string;
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    logoType: 'text' | 'image';
    logoUrl?: string;
    logoText?: string;
    faviconUrl?: string;
  };
};

// Initial state for the form data
const initialState: WebsiteFormData = {
  step: 1,
  name: '',
  category: '',
  description: '',
  contact: {
    email: '',
    phone: '',
    address: '',
    socialMedia: {},
  },
  images: [],
  uploadedImages: [],
  selectedImages: [],
  selectedTemplate: null,
  selectedSitePath: null,
  previewKey: null,  
  imageDimensions: {},
  customization: {
    fontFamily: 'Inter',
    primaryColor: '#3B82F6',
    secondaryColor: '#64748B',
    accentColor: '#F97316',
    logoType: 'text',
    logoUrl: '',
    faviconUrl: '',
  },
};

type SiteBuilderContextType = {
  formData: WebsiteFormData;
  updateFormData: (data: Partial<WebsiteFormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  resetForm: () => void;
  validateStep: () => boolean;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  isGlobalLoading: boolean;
  setGlobalLoading: (loading: boolean) => void;
};

// Create the context
const SiteBuilderContext = createContext<SiteBuilderContextType | undefined>(undefined);

// Provider component
export const SiteBuilderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [formData, setFormData] = useState<WebsiteFormData>(initialState);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isGlobalLoading, setGlobalLoading] = useState(false);

  const updateFormData = (data: Partial<WebsiteFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (formData.step < 7) {
      setFormData(prev => ({ ...prev, step: prev.step + 1 }));
    }
  };

  const prevStep = () => {
    if (formData.step > 1) {
      setFormData(prev => ({ ...prev, step: prev.step - 1 }));
    }
  };

  const goToStep = (step: number) => {
    if (step >= 1 && step <= 7) {
      setFormData(prev => ({ ...prev, step }));
    }
  };

  const resetForm = () => {
    setFormData(initialState);
  };

  const validateStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.step === 1) {
      if (!formData.name.trim()) newErrors.name = 'Website name is required';
      if (!formData.category) newErrors.category = 'Please select the category of the website';
    }

    if (formData.step === 2 && !formData.description.trim()) {
      newErrors.description = 'Please describe your website';
    }

    // if (formData.step === 3) {
    //   if (!formData.contact.email.trim()) {
    //     newErrors.email = 'Email is required';
    //   } else if (!/\S+@\S+\.\S+/.test(formData.contact.email)) {
    //     newErrors.email = 'Enter a valid email address';
    //   }
    // }

    if (formData.step === 4) {
      // When moving to next step, convert selectedImages to images array
      updateFormData({
        images: formData.selectedImages.map((url) => ({
          url,
          id: '',
          isAIGenerated: false,
        })),
      });
    }

    if (formData.step === 5) {
      if (formData.selectedTemplate === null) {
        newErrors.selectedTemplate = 'Please select a template before continuing';
      }
    }


    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const value = {
    formData,
    updateFormData,
    nextStep,
    prevStep,
    goToStep,
    resetForm,
    validateStep,
    errors,
    setErrors,
    isGlobalLoading,
    setGlobalLoading
  };

  return (
    <SiteBuilderContext.Provider value={value}>
      {children}
    </SiteBuilderContext.Provider>
  );
};

// Custom hook to use the context
export const useSiteBuilder = () => {
  const context = useContext(SiteBuilderContext);
  if (context === undefined) {
    throw new Error('useSiteBuilder must be used within a SiteBuilderProvider');
  }
  return context;
};
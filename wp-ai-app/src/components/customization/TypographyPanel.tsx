// 'use client';

// import React, { useState } from 'react';
// import { FiChevronLeft } from 'react-icons/fi';
// import { FONT_OPTIONS } from './constants';

// export default function TypographyPanel({ onBack }: { onBack: () => void }) {
//   const [selectedFont, setSelectedFont] = useState(FONT_OPTIONS[0].value);

//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col">
//       <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
//         <FiChevronLeft className="cursor-pointer" onClick={onBack} />
//         <span>Typography</span>
//       </div>

//       <div className="p-4">
//         <h3 className="text-sm font-semibold text-gray-700 mb-1">Fonts</h3>
//         <select
//           value={selectedFont}
//           onChange={(e) => setSelectedFont(e.target.value)}
//           className="w-full border border-gray-300 rounded-md p-2 text-sm"
//         >
//           {FONT_OPTIONS.map((font) => (
//             <option key={font.value} value={font.value}>
//               {font.label}
//             </option>
//           ))}
//         </select>
//       </div>
//     </div>
//   );
// }


'use client';

import React from 'react';
import { FiChevronLeft } from 'react-icons/fi';
import { FONT_OPTIONS } from './constants';
import { useCustomization } from './CustomizationProvider';

export default function TypographyPanel({ onBack }: { onBack: () => void }) {
  const { theme, updateTheme } = useCustomization();

  const handleFontChange = (key: 'headingFont' | 'bodyFont') => (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateTheme(key, e.target.value);

    // Live update iframe preview
    const iframe = document.querySelector<HTMLIFrameElement>('iframe');
    if (iframe) {
      const url = new URL(iframe.src);
      url.searchParams.set(key, e.target.value);
      url.searchParams.set('_refresh', Date.now().toString());
      iframe.src = url.toString();
    }
  };

  return (
    <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col">
      <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
        <FiChevronLeft className="cursor-pointer" onClick={onBack} />
        <span>Typography</span>
      </div>

      <div className="p-4 space-y-6">
        <div>
          <h3 className="text-sm font-semibold text-gray-700 mb-1">Heading Font</h3>
          <select
            value={theme.headingFont}
            onChange={handleFontChange('headingFont')}
            className="w-full border border-gray-300 rounded-md p-2 text-sm"
          >
            {FONT_OPTIONS.map((font) => (
              <option key={font.value} value={font.value}>
                {font.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <h3 className="text-sm font-semibold text-gray-700 mb-1">Body Font</h3>
          <select
            value={theme.bodyFont}
            onChange={handleFontChange('bodyFont')}
            className="w-full border border-gray-300 rounded-md p-2 text-sm"
          >
            {FONT_OPTIONS.map((font) => (
              <option key={font.value} value={font.value}>
                {font.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}

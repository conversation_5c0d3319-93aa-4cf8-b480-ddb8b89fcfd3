// 'use client';

// import { FONT_OPTIONS } from './constants';
// import { useCustomization } from './CustomizationProvider';

// export const ThemeControls = () => {
//   const { theme, updateTheme } = useCustomization(); 

//   const sendThemeUpdate = async (newTheme: typeof theme) => {
//     try {
//       await fetch(`${process.env.NEXT_PUBLIC_WORDPRESS_API}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify(newTheme),
//       });

//       // 🔁 Refresh the iframe preview with a cache-busting param
//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }
//     } catch (err) {
//       console.error('Failed to update theme settings:', err);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       {/* Color Picker Section */}
//       <div className="space-y-4">
//         <h3 className="text-lg font-semibold">Color Scheme</h3>

//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Primary Color</label>
//           <div className="flex items-center gap-2">
//             <input
//               type="color"
//               className="w-12 h-12 rounded cursor-pointer"
//               value={theme.primaryColor}
//               onChange={(e) => {
//                 const value = e.target.value;
//                 updateTheme('primaryColor', value);
//                 sendThemeUpdate({ ...theme, primaryColor: value });
//               }}
//             />
//             <span className="text-sm">{theme.primaryColor}</span>
//           </div>
//         </div>

//         {/* Add secondary color if needed */}
//         {/* 
//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Secondary Color</label>
//           <div className="flex items-center gap-2">
//             <input
//               type="color"
//               className="w-12 h-12 rounded cursor-pointer"
//               value={theme.secondaryColor}
//               onChange={(e) => {
//                 const value = e.target.value;
//                 updateTheme('secondaryColor', value);
//                 sendThemeUpdate({ ...theme, secondaryColor: value });
//               }}
//             />
//             <span className="text-sm">{theme.secondaryColor}</span>
//           </div>
//         </div>
//         */}
//       </div>

//       {/* Font Selector Section */}
//       <div className="space-y-4">
//         <h3 className="text-lg font-semibold">Typography</h3>

//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Heading Font</label>
//           <select
//             className="w-full p-2 rounded border"
//             value={theme.headingFont}
//             onChange={(e) => {
//               const value = e.target.value;
//               updateTheme('headingFont', value);
//               sendThemeUpdate({ ...theme, headingFont: value });
//             }}
//           >
//             {FONT_OPTIONS.map(font => (
//               <option 
//                 key={font.value} 
//                 value={font.value}
//                 style={{ fontFamily: font.value }}
//               >
//                 {font.label}
//               </option>
//             ))}
//           </select>
//         </div>

//         {/* Body font selection (optional) */}
//         {/* 
//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Body Font</label>
//           <select
//             className="w-full p-2 rounded border"
//             value={theme.bodyFont}
//             onChange={(e) => {
//               const value = e.target.value;
//               updateTheme('bodyFont', value);
//               sendThemeUpdate({ ...theme, bodyFont: value });
//             }}
//           >
//             {FONT_OPTIONS.map(font => (
//               <option 
//                 key={font.value} 
//                 value={font.value}
//                 style={{ fontFamily: font.value }}
//               >
//                 {font.label}
//               </option>
//             ))}
//           </select>
//         </div>
//         */}
//       </div>
//     </div>
//   );
// };


// 'use client';

// import { FONT_OPTIONS } from './constants';
// import { useCustomization } from './CustomizationProvider';

// export const ThemeControls = ({ sitePath }: { sitePath: string }) => {
//   const { theme, updateTheme } = useCustomization(); 

//   const sendThemeUpdate = async (newTheme: typeof theme) => {
//     try {
//       // Get the blog ID from the sitePath (e.g., extract "2" from "http://localhost/design-002")
//       const blogId = sitePath.match(/design-(\d+)/)?.[1] || '1';
      
//       await fetch(`${sitePath}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({
//           ...newTheme,
//           blog_id: blogId
//         }),
//       });

//       // Force iframe reload with theme settings in URL
//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
//         url.searchParams.set('primaryColor', newTheme.primaryColor);
//         url.searchParams.set('headingFont', newTheme.headingFont);
//         url.searchParams.set('bodyFont', newTheme.bodyFont);
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }
//     } catch (err) {
//       console.error('Failed to update theme settings:', err);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       {/* Color Picker Section */}
//       <div className="space-y-4">
//         <h3 className="text-lg font-semibold">Color Scheme</h3>

//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Primary Color</label>
//           <div className="flex items-center gap-2">
//             <input
//               type="color"
//               className="w-12 h-12 rounded cursor-pointer"
//               value={theme.primaryColor}
//               onChange={(e) => {
//                 const value = e.target.value;
//                 updateTheme('primaryColor', value);
//                 sendThemeUpdate({ ...theme, primaryColor: value });
//               }}
//             />
//             <span className="text-sm">{theme.primaryColor}</span>
//           </div>
//         </div>

//         {/* Add secondary color if needed */}
//         {/* 
//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Secondary Color</label>
//           <div className="flex items-center gap-2">
//             <input
//               type="color"
//               className="w-12 h-12 rounded cursor-pointer"
//               value={theme.secondaryColor}
//               onChange={(e) => {
//                 const value = e.target.value;
//                 updateTheme('secondaryColor', value);
//                 sendThemeUpdate({ ...theme, secondaryColor: value });
//               }}
//             />
//             <span className="text-sm">{theme.secondaryColor}</span>
//           </div>
//         </div>
//         */}
//       </div>

//       {/* Font Selector Section */}
//       <div className="space-y-4">
//         <h3 className="text-lg font-semibold">Typography</h3>

//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Heading Font</label>
//           <select
//             className="w-full p-2 rounded border"
//             value={theme.headingFont}
//             onChange={(e) => {
//               const value = e.target.value;
//               updateTheme('headingFont', value);
//               sendThemeUpdate({ ...theme, headingFont: value });
//             }}
//           >
//             {FONT_OPTIONS.map(font => (
//               <option 
//                 key={font.value} 
//                 value={font.value}
//                 style={{ fontFamily: font.value }}
//               >
//                 {font.label}
//               </option>
//             ))}
//           </select>
//         </div>

//         {/* Body font selection (optional) */}
//         {/* 
//         <div className="space-y-2">
//           <label className="block text-sm font-medium">Body Font</label>
//           <select
//             className="w-full p-2 rounded border"
//             value={theme.bodyFont}
//             onChange={(e) => {
//               const value = e.target.value;
//               updateTheme('bodyFont', value);
//               sendThemeUpdate({ ...theme, bodyFont: value });
//             }}
//           >
//             {FONT_OPTIONS.map(font => (
//               <option 
//                 key={font.value} 
//                 value={font.value}
//                 style={{ fontFamily: font.value }}
//               >
//                 {font.label}
//               </option>
//             ))}
//           </select>
//         </div>
//         */}
//       </div>
//     </div>
//   );
// };




'use client';
import { FONT_OPTIONS } from './constants';
import { useCustomization } from './CustomizationProvider';

export const ThemeControls = ({ sitePath }: { sitePath: string }) => {
  const { theme, updateTheme } = useCustomization(); 

  const sendThemeUpdate = async (newTheme: typeof theme) => {
    try {
      const blogId = sitePath.match(/design-(\d+)/)?.[1] || '1';

      const response = await fetch(`${sitePath}/wp-json/custom/v1/update-theme-settings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...newTheme, blog_id: blogId }),
      });

      if (!response.ok) throw new Error('Failed to update theme settings');

      const iframe = document.querySelector<HTMLIFrameElement>('iframe');
      if (iframe) {
        const url = new URL(sitePath);
        const currentParams = new URL(iframe.src).searchParams;

        const previewKey = currentParams.get('preview_key');
        const heroImageUrl = currentParams.get('heroImageUrl');
        const headerImageUrl = currentParams.get('headerImageUrl');

        if (previewKey) url.searchParams.set('preview_key', previewKey);
        if (heroImageUrl) url.searchParams.set('heroImageUrl', heroImageUrl);
        if (headerImageUrl) url.searchParams.set('headerImageUrl', headerImageUrl);

        url.searchParams.set('primaryColor', newTheme.primaryColor);
        url.searchParams.set('headingFont', newTheme.headingFont);
        url.searchParams.set('bodyFont', newTheme.bodyFont);
        url.searchParams.set('_refresh', Date.now().toString());

        if (iframe.src !== url.toString()) {
          iframe.src = url.toString();
        }
      }
    } catch (err) {
      console.error('Failed to update theme settings:', err);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Color Scheme</h3>
        <div className="space-y-2">
          <label className="block text-sm font-medium">Primary Color</label>
          <div className="flex items-center gap-2">
            <input
              type="color"
              className="w-12 h-12 rounded cursor-pointer"
              value={theme.primaryColor}
              onChange={(e) => {
                const value = e.target.value;
                updateTheme('primaryColor', value);
                sendThemeUpdate({ ...theme, primaryColor: value });
              }}
            />
            <span className="text-sm">{theme.primaryColor}</span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Typography</h3>

        <div className="space-y-2">
          <label className="block text-sm font-medium">Heading Font</label>
          <select
            className="w-full p-2 rounded border"
            value={theme.headingFont}
            onChange={(e) => {
              const value = e.target.value;
              updateTheme('headingFont', value);
              sendThemeUpdate({ ...theme, headingFont: value });
            }}
          >
            {FONT_OPTIONS.map(font => (
              <option key={font.value} value={font.value} style={{ fontFamily: font.value }}>
                {font.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
};

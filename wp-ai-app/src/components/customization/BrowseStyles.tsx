// //this is working as expected without variation implementation, varation are not applied on the themes
// 'use client';

// import React from 'react';
// import { FiChevronLeft } from 'react-icons/fi';

// const styles = [
//   { name: 'Purple Haze', bg: 'bg-gradient-to-br from-purple-100 to-purple-50', colors: ['bg-gray-700', 'bg-purple-500'] },
//   { name: 'Ocean Blue', bg: 'bg-[#10324E]', colors: ['bg-gray-300', 'bg-cyan-400'] },
//   { name: 'Dark Grape', bg: 'bg-[#2B1E3D]', colors: ['bg-gray-400', 'bg-purple-300'] },
//   { name: 'Orange Pop', bg: 'bg-orange-100', colors: ['bg-gray-700', 'bg-orange-400'] },
//   { name: 'Pink Candy', bg: 'bg-pink-100', colors: ['bg-gray-700', 'bg-pink-600'] },
//   { name: 'Mint Fresh', bg: 'bg-green-100', colors: ['bg-gray-700', 'bg-green-500'] },
//   { name: 'Midnight', bg: 'bg-gray-900', colors: ['bg-gray-300', 'bg-yellow-300'] },
//   { name: 'Sky Shine', bg: 'bg-blue-100', colors: ['bg-gray-700', 'bg-blue-500'] },
// ];

// export default function BrowseStyles({
//   onBack,
//   onSelectStyle,
// }: {
//   onBack: () => void;
//   onSelectStyle: (style: { name: string; bg: string; colors: string[] }) => void;
// }) {
//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col px-4 py-6">
//       <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={onBack}>
//         <FiChevronLeft />
//         <span className="text-sm font-medium">Browse styles</span>
//       </div>

//       <p className="text-sm text-gray-600 mb-4">
//         Choose a variation to change the look of the site.
//       </p>

//       <div className="grid grid-cols-2 gap-4 overflow-y-auto">
//         {styles.map((style, i) => (
//           <div
//             key={i}
//             className="perspective cursor-pointer"
//             onClick={() => onSelectStyle(style)}
//           >
//             <div className="flip-card">
//               {/* Front */}
//               <div className={`flip-card-front absolute inset-0 rounded-md p-3 flex flex-col justify-between ${style.bg}`}>
//                 <span className="text-xl font-bold text-white">Aa</span>
//                 <div className="flex gap-1">
//                   {style.colors.map((c, idx) => (
//                     <div key={idx} className={`w-4 h-4 rounded-full border border-white ${c}`} />
//                   ))}
//                 </div>
//               </div>
//               {/* Back */}
//               <div className="flip-card-back absolute inset-0 bg-white border rounded-md flex items-center justify-center text-xs font-medium">
//                 {style.name}
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       <style jsx>{`
//         .perspective {
//           perspective: 1000px;
//         }
//         .flip-card {
//           width: 100%;
//           height: 96px;
//           position: relative;
//           transform-style: preserve-3d;
//           transition: transform 0.6s;
//         }
//         .flip-card:hover {
//           transform: rotateY(180deg);
//         }
//         .flip-card-front,
//         .flip-card-back {
//           backface-visibility: hidden;
//         }
//         .flip-card-back {
//           transform: rotateY(180deg);
//         }
//       `}</style>
//     </div>
//   );
// }


// 'use client';

// import React from 'react';
// import { FiChevronLeft } from 'react-icons/fi';
// import { useCustomization } from './CustomizationProvider';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';

// const styles = [
//   { name: 'Purple Haze', bg: 'bg-gradient-to-br from-purple-100 to-purple-50', colors: ['#374151', '#a855f7'], primaryColor: '#a855f7' },
//   { name: 'Ocean Blue', bg: 'bg-[#10324E]', colors: ['#d1d5db', '#22d3ee'], primaryColor: '#22d3ee' },
//   { name: 'Dark Grape', bg: 'bg-[#2B1E3D]', colors: ['#9ca3af', '#c084fc'], primaryColor: '#c084fc' },
//   { name: 'Orange Pop', bg: 'bg-orange-100', colors: ['#374151', '#fb923c'], primaryColor: '#fb923c' },
//   { name: 'Pink Candy', bg: 'bg-pink-100', colors: ['#374151', '#ec4899'], primaryColor: '#ec4899' },
//   { name: 'Mint Fresh', bg: 'bg-green-100', colors: ['#374151', '#22c55e'], primaryColor: '#22c55e' },
//   { name: 'Midnight', bg: 'bg-gray-900', colors: ['#d1d5db', '#facc15'], primaryColor: '#facc15' },
//   { name: 'Sky Shine', bg: 'bg-blue-100', colors: ['#374151', '#3b82f6'], primaryColor: '#3b82f6' },
// ];

// export default function BrowseStyles({ onBack }: { onBack: () => void }) {
//   const { setTheme, theme } = useCustomization();
//   const { formData } = useSiteBuilder();

//   const handleStyleSelect = async (style: typeof styles[number]) => {
//     setTheme(prev => ({
//       ...prev,
//       name: style.name,
//       bg: style.bg,
//       colors: style.colors,
//       primaryColor: style.primaryColor,
//     }));

//     const blogId = formData.selectedSitePath?.match(/design-(\d+)/)?.[1] || '1';

//     await fetch(`${formData.selectedSitePath}/wp-json/custom/v1/update-theme-settings`, {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify({
//         primaryColor: style.primaryColor,
//         headingFont: theme.headingFont,
//         bodyFont: theme.bodyFont,
//         blog_id: blogId,
//       }),
//     });

//     // Update preview iframe
//     const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//     if (iframe) {
//       const url = new URL(iframe.src);
//       url.searchParams.set('primaryColor', style.primaryColor);
//       url.searchParams.set('headingFont', theme.headingFont);
//       url.searchParams.set('bodyFont', theme.bodyFont);
//       url.searchParams.set('heroImageUrl', formData.images[0]?.url || '');
//       url.searchParams.set('headerImageUrl', formData.images[1]?.url || '');
//       url.searchParams.set('preview_key', Date.now().toString()); // Cache bust
//       iframe.src = url.toString();
//     }

//     onBack();
//   };

//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col px-4 py-6">
//       <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={onBack}>
//         <FiChevronLeft />
//         <span className="text-sm font-medium">Browse styles</span>
//       </div>

//       <p className="text-sm text-gray-600 mb-4">Choose a variation to change the look of the site.</p>

//       <div className="grid grid-cols-2 gap-4 overflow-y-auto">
//         {styles.map((style, i) => (
//           <div key={i} className="perspective cursor-pointer" onClick={() => handleStyleSelect(style)}>
//             <div className="flip-card">
//               <div className={`flip-card-front absolute inset-0 rounded-md p-3 flex flex-col justify-between ${style.bg}`}>
//                 <span className="text-xl font-bold text-white">Aa</span>
//                 <div className="flex gap-1">
//                   {style.colors.map((c, idx) => (
//                     <div key={idx} className={`w-4 h-4 rounded-full border border-white`} style={{ backgroundColor: c }} />
//                   ))}
//                 </div>
//               </div>
//               <div className="flip-card-back absolute inset-0 bg-white border rounded-md flex items-center justify-center text-xs font-medium">
//                 {style.name}
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       <style jsx>{`
//         .perspective { perspective: 1000px; }
//         .flip-card {
//           width: 100%; height: 96px; position: relative;
//           transform-style: preserve-3d; transition: transform 0.6s;
//         }
//         .flip-card:hover { transform: rotateY(180deg); }
//         .flip-card-front, .flip-card-back { backface-visibility: hidden; }
//         .flip-card-back { transform: rotateY(180deg); }
//       `}</style>
//     </div>
//   );
// }

//hardcode the styles for now, we can add more later, and this is working as expected 
// 'use client';

// import React from 'react';
// import { FiChevronLeft } from 'react-icons/fi';
// import { useCustomization } from './CustomizationProvider';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';
// import { THEME_STYLES } from './themeStyles';

// const styles = [
//   { name: 'Purple Haze', bg: 'bg-gradient-to-br from-purple-100 to-purple-50', colors: ['#374151', '#a855f7'], primaryColor: '#a855f7' },
//   { name: 'Ocean Blue', bg: 'bg-[#10324E]', colors: ['#d1d5db', '#22d3ee'], primaryColor: '#22d3ee' },
//   { name: 'Dark Grape', bg: 'bg-[#2B1E3D]', colors: ['#9ca3af', '#c084fc'], primaryColor: '#c084fc' },
//   { name: 'Orange Pop', bg: 'bg-orange-100', colors: ['#374151', '#fb923c'], primaryColor: '#fb923c' },
//   { name: 'Pink Candy', bg: 'bg-pink-100', colors: ['#374151', '#ec4899'], primaryColor: '#ec4899' },
//   { name: 'Mint Fresh', bg: 'bg-green-100', colors: ['#374151', '#22c55e'], primaryColor: '#22c55e' },
//   { name: 'Midnight', bg: 'bg-gray-900', colors: ['#d1d5db', '#facc15'], primaryColor: '#facc15' },
//   { name: 'Sky Shine', bg: 'bg-blue-100', colors: ['#374151', '#3b82f6'], primaryColor: '#3b82f6' },
// ];

// export default function BrowseStyles({ onBack }: { onBack: () => void }) {
//   const { setTheme, theme } = useCustomization();
//   const { formData } = useSiteBuilder(); // Get formData from useSiteBuilder

//   const handleStyleSelect = async (style: typeof styles[number]) => {
//     // Update the theme state in CustomizationProvider
//     setTheme(prev => ({
//       ...prev,
//       bg: style.bg,
//       colors: style.colors,
//       primaryColor: style.primaryColor,
//       // Ensure other properties like name are preserved from the previous state
//       name: prev.name, // This line correctly preserves the name in the theme context
//       secondaryColor: prev.secondaryColor, // Ensure these are also preserved if they exist
//       headingFont: prev.headingFont,
//       bodyFont: prev.bodyFont,
//     }));

//     const blogId = formData.selectedSitePath?.match(/design-(\d+)/)?.[1] || '1';

//     // Send update to WordPress backend (ensure relevant data is sent)
//     await fetch(`${formData.selectedSitePath}/wp-json/custom/v1/update-theme-settings`, {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify({
//         primaryColor: style.primaryColor,
//         // Make sure to send other relevant theme settings from the *current* theme state
//         // to ensure the backend has the most complete picture.
//         headingFont: theme.headingFont, // Use theme from context, which should be updated or default
//         bodyFont: theme.bodyFont,     // Use theme from context
//         blog_id: blogId,
//         // Potentially include siteName if your WordPress endpoint uses it for theme settings
//         // Although formData.name is used for the iframe, the backend might need it too for consistency.
//         // siteName: formData.name, // Consider uncommenting if backend needs it
//       }),
//     });

//     // Update preview iframe
//     const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//     if (iframe) {
//       const url = new URL(iframe.src);

//       // Set the new style parameters
//       url.searchParams.set('primaryColor', style.primaryColor);

//       // Preserve existing theme settings from the context
//       url.searchParams.set('headingFont', theme.headingFont);
//       url.searchParams.set('bodyFont', theme.bodyFont);

//       // Crucially, ensure the siteName and image URLs are always passed from formData
//       url.searchParams.set('siteName', formData.name || '');
//       url.searchParams.set('heroImageUrl', formData.images[0]?.url || '');
//       url.searchParams.set('headerImageUrl', formData.images[1]?.url || '');

//       // Add a cache busting parameter
//       url.searchParams.set('_refresh', Date.now().toString()); // Use a more descriptive name if possible like 'timestamp'

//       iframe.src = url.toString();
//     }

//     onBack();
//   };

//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col px-4 py-6">
//       <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={onBack}>
//         <FiChevronLeft />
//         <span className="text-sm font-medium">Browse styles</span>
//       </div>

//       <p className="text-sm text-gray-600 mb-4">Choose a variation to change the look of the site.</p>

//       <div className="grid grid-cols-2 gap-4 overflow-y-auto">
//         {styles.map((style, i) => (
//           <div key={i} className="perspective cursor-pointer" onClick={() => handleStyleSelect(style)}>
//             <div className="flip-card">
//               <div className={`flip-card-front absolute inset-0 rounded-md p-3 flex flex-col justify-between ${style.bg}`}>
//                 <span className="text-xl font-bold text-white">Aa</span>
//                 <div className="flex gap-1">
//                   {style.colors.map((c, idx) => (
//                     <div key={idx} className={`w-4 h-4 rounded-full border border-white`} style={{ backgroundColor: c }} />
//                   ))}
//                 </div>
//               </div>
//               <div className="flip-card-back absolute inset-0 bg-white border rounded-md flex items-center justify-center text-xs font-medium">
//                 {style.name}
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       <style jsx>{`
//         .perspective { perspective: 1000px; }
//         .flip-card {
//           width: 100%; height: 96px; position: relative;
//           transform-style: preserve-3d; transition: transform 0.6s;
//         }
//         .flip-card:hover { transform: rotateY(180deg); }
//         .flip-card-front, .flip-card-back { backface-visibility: hidden; }
//         .flip-card-back { transform: rotateY(180deg); }
//       `}</style>
//     </div>
//   );
// }

//using THEME_STYLES from themeStyles.ts
'use client';

import React from 'react';
import { FiChevronLeft } from 'react-icons/fi';
import { useCustomization } from './CustomizationProvider';
import { useSiteBuilder } from '@/context/SiteBuilderContext';
import { THEME_STYLES } from './themeStyles';

export default function BrowseStyles({ onBack }: { onBack: () => void }) {
  const { setTheme, theme } = useCustomization();
  const { formData } = useSiteBuilder();

  const handleStyleSelect = async (style: typeof THEME_STYLES[number]) => {
    // Update theme context with the selected style
    setTheme(prev => ({
      ...prev,
      ...style, // includes primaryColor, secondaryColor, bg, colors, fonts, etc.
    }));

    const blogId = formData.selectedSitePath?.match(/design-(\d+)/)?.[1] || '1';

    await fetch(`${formData.selectedSitePath}/wp-json/custom/v1/update-theme-settings`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        blog_id: blogId,
        primaryColor: style.primaryColor,
        secondaryColor: style.secondaryColor,
        backgroundColor: style.backgroundColor,
        textColor: style.textColor,
        headingColor: style.headingColor,
        headingFont: theme.headingFont,
        bodyFont: theme.bodyFont,
      }),
    });

    // Update preview iframe
    const iframe = document.querySelector<HTMLIFrameElement>('iframe');
    if (iframe) {
      const url = new URL(iframe.src);
      url.searchParams.set('primaryColor', style.primaryColor);
      url.searchParams.set('secondaryColor', style.secondaryColor);
      url.searchParams.set('backgroundColor', style.backgroundColor);
      url.searchParams.set('textColor', style.textColor);
      url.searchParams.set('headingColor', style.headingColor);
      url.searchParams.set('headingFont', style.headingFont);
      url.searchParams.set('bodyFont', style.bodyFont);
      url.searchParams.set('siteName', formData.name || '');
      url.searchParams.set('heroImageUrl', formData.images[0]?.url || '');
      url.searchParams.set('headerImageUrl', formData.images[1]?.url || '');
      url.searchParams.set('_refresh', Date.now().toString());

      iframe.src = url.toString();
    }

    onBack();
  };

  return (
    <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col px-4 py-6">
      <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={onBack}>
        <FiChevronLeft />
        <span className="text-sm font-medium">Browse styles</span>
      </div>

      <p className="text-sm text-gray-600 mb-4">Choose a variation to change the look of the site.</p>

      <div className="grid grid-cols-2 gap-4 overflow-y-auto">
        {THEME_STYLES.map((style, i) => (
          <div key={i} className="perspective cursor-pointer" onClick={() => handleStyleSelect(style)}>
            <div className="flip-card">
              <div className={`flip-card-front absolute inset-0 rounded-md p-3 flex flex-col justify-between ${style.bg}`}>
                <span className="text-xl font-bold text-white">Aa</span>
                <div className="flex gap-1">
                  {style.colors.map((c, idx) => (
                    <div key={idx} className="w-4 h-4 rounded-full border border-white" style={{ backgroundColor: c }} />
                  ))}
                </div>
              </div>
              <div className="flip-card-back absolute inset-0 bg-white border rounded-md flex items-center justify-center text-xs font-medium">
                {style.name}
              </div>
            </div>
          </div>
        ))}
      </div>

      <style jsx>{`
        .perspective {
          perspective: 1000px;
        }
        .flip-card {
          width: 100%;
          height: 96px;
          position: relative;
          transform-style: preserve-3d;
          transition: transform 0.6s;
        }
        .flip-card:hover {
          transform: rotateY(180deg);
        }
        .flip-card-front,
        .flip-card-back {
          backface-visibility: hidden;
        }
        .flip-card-back {
          transform: rotateY(180deg);
        }
      `}</style>
    </div>
  );
}

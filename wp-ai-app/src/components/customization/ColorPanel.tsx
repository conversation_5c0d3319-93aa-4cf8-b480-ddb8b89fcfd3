'use client';

import React from 'react';
import { FiChevronLeft } from 'react-icons/fi';

export default function ColorPanel({
  onBack,
  onNavigateEditPalette,
  selectedStyle,
}: {
  onBack: () => void;
  onNavigateEditPalette: () => void;
  selectedStyle: { name: string; bg: string; colors: string[] };
}) {
  return (
    <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col">
      {/* Header */}
      <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
        <FiChevronLeft className="cursor-pointer" onClick={onBack} />
        <span>Styles</span>
      </div>

      {/* Palette Section */}
      <div className="p-4">
        <h2 className="text-sm font-semibold text-gray-700 mb-2">PALETTE</h2>
        <div className="flex items-center justify-between border rounded px-3 py-2 cursor-pointer" onClick={onNavigateEditPalette}>
          <div className="flex gap-1">
            {selectedStyle.colors.map((color, index) => (
              <div
                className="w-5 h-5 rounded-full border border-gray-200"
                style={{ backgroundColor: color }}
                />
            ))}
          </div>
          <span className="text-sm text-gray-600 font-medium">Edit palette</span>
        </div>
      </div>
    </div>
  );
}

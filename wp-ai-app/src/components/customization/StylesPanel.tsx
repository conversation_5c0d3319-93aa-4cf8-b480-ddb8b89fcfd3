'use client';

import React from 'react';
import { FiChevronRight } from 'react-icons/fi';
import { MdOutlineFormatColorText } from 'react-icons/md';
import { PiDropFill } from 'react-icons/pi';

export default function StylesPanel({
  onNavigate,
  selectedStyle,
}: {
  onNavigate: (panel: 'browse' | 'typography' | 'colors') => void;
  selectedStyle: {
    name: string;
    bg: string;
    colors: string[];
  };
}) {
  return (
    <div className="h-full w-72 bg-white text-black border-l border-gray-300 flex flex-col px-4 py-6">
      <h2 className="text-lg font-semibold mb-6">Styles</h2>

      <div className="flex-1 flex flex-col justify-center space-y-10">
        <div>
          <div className={`w-full h-32 flex items-center justify-center text-6xl font-bold text-white rounded-md mb-3 ${selectedStyle.bg}`}>
            Aa
          </div>
          <button
            onClick={() => onNavigate('browse')}
            className="w-full flex items-center justify-between text-sm font-medium text-gray-700 hover:text-black"
          >
            Browse styles <FiChevronRight />
          </button>
        </div>

        <button
          className="flex items-center gap-2 text-sm text-gray-700 hover:text-black"
          onClick={() => onNavigate('typography')}
        >
          <MdOutlineFormatColorText className="text-lg" /> Typography
        </button>

        <button
          className="flex items-center gap-2 text-sm text-gray-700 hover:text-black"
          onClick={() => onNavigate('colors')}
        >
          <PiDropFill className="text-lg" /> Colors
        </button>
      </div>
    </div>
  );
}

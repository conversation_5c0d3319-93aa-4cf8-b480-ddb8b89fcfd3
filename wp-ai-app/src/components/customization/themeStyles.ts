import { ThemeSettings } from './types';

export const THEME_STYLES: ThemeSettings[] = [
  {
    name: 'Purple Haze',
    bg: 'bg-gradient-to-br from-purple-100 to-purple-50',
    colors: ['#374151', '#a855f7', '#ffffff', '#000000', '#1e293b'],
    primaryColor: '#a855f7',
    secondaryColor: '#374151',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    headingColor: '#1e293b',
    headingFont: 'Playfair Display',
    bodyFont: 'Roboto',
  },
  {
    name: 'Ocean Blue',
    bg: 'bg-[#10324E]',
    colors: ['#d1d5db', '#22d3ee', '#ffffff', '#1f2937', '#0f172a'],
    primaryColor: '#22d3ee',
    secondaryColor: '#10324E',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    headingColor: '#0f172a',
    headingFont: 'Lora',
    bodyFont: 'Open Sans',
  },
  {
    name: 'Dark Grape',
    bg: 'bg-[#2B1E3D]',
    colors: ['#9ca3af', '#c084fc', '#f9fafb', '#1f2937', '#2e1065'],
    primaryColor: '#c084fc',
    secondaryColor: '#2B1E3D',
    backgroundColor: '#f9fafb',
    textColor: '#1f2937',
    headingColor: '#2e1065',
    headingFont: 'Merriweather',
    bodyFont: 'Montserrat',
  },
  {
    name: 'Orange Pop',
    bg: 'bg-orange-100',
    colors: ['#374151', '#fb923c', '#ffffff', '#1e293b', '#7c2d12'],
    primaryColor: '#fb923c',
    secondaryColor: '#374151',
    backgroundColor: '#ffffff',
    textColor: '#1e293b',
    headingColor: '#7c2d12',
    headingFont: 'Raleway',
    bodyFont: 'Poppins',
  },
  {
    name: 'Pink Candy',
    bg: 'bg-pink-100',
    colors: ['#374151', '#ec4899', '#ffffff', '#111827', '#831843'],
    primaryColor: '#ec4899',
    secondaryColor: '#374151',
    backgroundColor: '#ffffff',
    textColor: '#111827',
    headingColor: '#831843',
    headingFont: 'Playfair Display',
    bodyFont: 'Lora',
  },
  {
    name: 'Mint Fresh',
    bg: 'bg-green-100',
    colors: ['#374151', '#22c55e', '#ffffff', '#1f2937', '#065f46'],
    primaryColor: '#22c55e',
    secondaryColor: '#374151',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    headingColor: '#065f46',
    headingFont: 'Source Sans Pro',
    bodyFont: 'Ubuntu',
  },
  {
    name: 'Midnight',
    bg: 'bg-gray-900',
    colors: ['#d1d5db', '#facc15', '#1f2937', '#f9fafb', '#eab308'],
    primaryColor: '#facc15',
    secondaryColor: '#1f2937',
    backgroundColor: '#1f2937',
    textColor: '#f9fafb',
    headingColor: '#eab308',
    headingFont: 'Oswald',
    bodyFont: 'Roboto',
  },
  {
    name: 'Sky Shine',
    bg: 'bg-blue-100',
    colors: ['#374151', '#3b82f6', '#ffffff', '#1e3a8a', '#0c4a6e'],
    primaryColor: '#3b82f6',
    secondaryColor: '#374151',
    backgroundColor: '#ffffff',
    textColor: '#1e3a8a',
    headingColor: '#0c4a6e',
    headingFont: 'Merriweather',
    bodyFont: 'Open Sans',
  },
];

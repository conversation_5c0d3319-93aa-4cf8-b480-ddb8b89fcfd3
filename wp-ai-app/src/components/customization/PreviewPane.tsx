// working but only showing the color picker and font selector
// 'use client';
// import { useCustomization } from './CustomizationProvider';
// import { useEffect } from 'react';


// export const PreviewPane = ({ templateHtml }: { templateHtml: string | null }) => {
//   const { theme } = useCustomization();

//   const generateStyleInjection = () => `
//     :root {
//       --primary-color: ${theme.primaryColor};
//       --heading-font: ${theme.headingFont};
//       --body-font: ${theme.bodyFont};
//     }
    
//     h1, h2, h3 {
//       font-family: var(--heading-font);
//     }
    
//     body {
//       font-family: var(--body-font);
//     }
//   `;

//   return (
//     <div className="relative h-full">
//       <style>{generateStyleInjection()}</style>
//       <div dangerouslySetInnerHTML={{ __html: templateHtml }} />
//     </div>
//   );
// };
//working but not displaying  the website

// 'use client';
// import { useCustomization } from './CustomizationProvider';
// import { useEffect } from 'react';
// import DOMPurify from 'dompurify';
// import { FONT_OPTIONS } from './constants';

// export const PreviewPane = ({ templateHtml }: { templateHtml: string | null }) => {
//   const { theme } = useCustomization();

// //   useEffect(() => {
// //     // Load Google Fonts
// //     const WebFont = require('webfontloader');
// //     WebFont.load({
// //       google: {
// //         families: [`${theme.headingFont}:400,700`, `${theme.bodyFont}:400`]
// //       }
// //     });
// //   }, [theme.headingFont, theme.bodyFont]);

//   const generateStyleInjection = () => `
//     :root {
//       --primary-color: ${theme.primaryColor};
//       --secondary-color: ${theme.secondaryColor};
//       --accent-color: ${theme.accentColor};
//       --heading-font: '${theme.headingFont}', ${FONT_OPTIONS.find(f => f.value === theme.headingFont)?.category};
//       --body-font: '${theme.bodyFont}', ${FONT_OPTIONS.find(f => f.value === theme.bodyFont)?.category};
//     }

//     * {
//       font-family: var(--body-font);
//     }
    
//     h1, h2, h3, h4, h5, h6 {
//       font-family: var(--heading-font);
//       color: var(--primary-color);
//     }
    
//     a, button {
//       color: var(--accent-color);
//     }
    
//     .bg-primary {
//       background-color: var(--primary-color);
//     }
//   `;

//   if (!templateHtml) return null;

//   return (
//     <div className="h-full w-full overflow-auto">
//       <style>{generateStyleInjection()}</style>
//       <div 
//         className="preview-container mx-auto"
//         dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(templateHtml) }}
//       />
//     </div>
//   );
// };



// 'use client';
// import { useCustomization } from './CustomizationProvider';
// import { useEffect, useState } from 'react';
// import DOMPurify from 'dompurify';
// import { FONT_OPTIONS } from './constants';

// export const PreviewPane = ({ templateHtml }: { templateHtml: string | null }) => {
//   const { theme } = useCustomization();
//   const [sanitizedHtml, setSanitizedHtml] = useState<string | null>(null);

//   useEffect(() => {
//     if (templateHtml) {
//       const cleanedHtml = DOMPurify.sanitize(templateHtml);
//       setSanitizedHtml(cleanedHtml);
//     }
//   }, [templateHtml]);

//   const generateStyleInjection = () => `
//     :root {
//       --primary-color: ${theme.primaryColor};
//       --secondary-color: ${theme.secondaryColor};
//       --accent-color: ${theme.accentColor};
//       --heading-font: '${theme.headingFont}', ${FONT_OPTIONS.find(f => f.value === theme.headingFont)?.category};
//       --body-font: '${theme.bodyFont}', ${FONT_OPTIONS.find(f => f.value === theme.bodyFont)?.category};
//     }

//     * {
//       font-family: var(--body-font) !important;
//     }
    
//     h1, h2, h3, h4, h5, h6 {
//       font-family: var(--heading-font) !important;
//       color: var(--primary-color) !important;
//     }
    
//     a, .btn, button {
//       color: var(--accent-color) !important;
//     }
    
//     header, .bg-primary {
//       background-color: var(--primary-color) !important;
//     }
    
//     .text-primary {
//       color: var(--primary-color) !important;
//     }
//   `;

//   if (!sanitizedHtml) return null;

//   return (
//     <div className="h-full w-full">
//         <iframe
//         title="Template Preview"
//         className="w-full h-full border-none"
//         sandbox="allow-same-origin allow-scripts"
//         srcDoc={`
//             <!DOCTYPE html>
//             <html>
//             <head>
//                 <style>
//                 ${generateStyleInjection()}
//                 </style>
//             </head>
//             <body>
//                 ${sanitizedHtml}
//             </body>
//             </html>
//         `}
//         />
//     </div>
//     );
// };



// PreviewPane.tsx
// 'use client';
// import { useCustomization } from './CustomizationProvider';
// import { useEffect, useState } from 'react';
// import DOMPurify from 'dompurify';
// import { FONT_OPTIONS } from './constants';

// export const PreviewPane = ({ templateHtml }: { templateHtml: string | null }) => {
//   const { theme } = useCustomization();
//   const [sanitizedHtml, setSanitizedHtml] = useState<string | null>(null);

//   useEffect(() => {
//     if (templateHtml) {
//       const cleanedHtml = DOMPurify.sanitize(templateHtml);
//       setSanitizedHtml(cleanedHtml);
//     }
//   }, [templateHtml]);

//   const generateStyleInjection = () => `
//     :root {
//       --primary-color: ${theme.primaryColor};
//       --secondary-color: ${theme.secondaryColor};
//       --accent-color: ${theme.accentColor};
//       --heading-font: '${theme.headingFont}', ${FONT_OPTIONS.find(f => f.value === theme.headingFont)?.category};
//       --body-font: '${theme.bodyFont}', ${FONT_OPTIONS.find(f => f.value === theme.bodyFont)?.category};
//     }

//     * {
//       font-family: var(--body-font) !important;
//     }
    
//     h1, h2, h3, h4, h5, h6 {
//       font-family: var(--heading-font) !important;
//       color: var(--primary-color) !important;
//     }
    
//     a, .btn, button {
//       color: var(--accent-color) !important;
//     }
    
//     header, .bg-primary {
//       background-color: var(--primary-color) !important;
//     }
    
//     .text-primary {
//       color: var(--primary-color) !important;
//     }
//   `;

//   if (!sanitizedHtml) return null;

//   return (
//     <div className="h-full w-full bg-white">
//       <iframe
//         className="w-full h-full border rounded-lg shadow-xl"
//         srcDoc={sanitizedHtml}
//         sandbox="allow-same-origin allow-scripts"
//         onLoad={(e) => {
//           const iframe = e.target as HTMLIFrameElement;
//           const style = document.createElement('style');
//           style.textContent = generateStyleInjection();
//           iframe.contentDocument?.head.appendChild(style);
//         }}
//       />
//     </div>
//   );
// };




import { useCustomization } from './CustomizationProvider';
import { useEffect, useState, useRef } from 'react';
import DOMPurify from 'dompurify';
import { FONT_OPTIONS } from './constants';

export const PreviewPane = ({ templateHtml }: { templateHtml: string | null }) => {
  const { theme } = useCustomization();
  const [sanitizedHtml, setSanitizedHtml] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (templateHtml) {
      const cleanedHtml = DOMPurify.sanitize(templateHtml);
      setSanitizedHtml(cleanedHtml);
    }
  }, [templateHtml]);

  const generateStyleInjection = () => `
    :root {
      --primary-color: ${theme.primaryColor};
      --secondary-color: ${theme.secondaryColor};
      --accent-color: ${theme.accentColor};
      --heading-font: '${theme.headingFont}', ${FONT_OPTIONS.find(f => f.value === theme.headingFont)?.category};
      --body-font: '${theme.bodyFont}', ${FONT_OPTIONS.find(f => f.value === theme.bodyFont)?.category};
    }
    * { font-family: var(--body-font) !important; }
    h1, h2, h3, h4, h5, h6 { font-family: var(--heading-font) !important; color: var(--primary-color) !important; }
    a, .btn, button { color: var(--accent-color) !important; }
    header, .bg-primary { background-color: var(--primary-color) !important; }
    .text-primary { color: var(--primary-color) !important; }
  `;

  // Inject styles whenever the theme changes
  useEffect(() => {
    const iframe = iframeRef.current;
    if (iframe && iframe.contentDocument) {
      let styleTag = iframe.contentDocument.getElementById('theme-style') as HTMLStyleElement | null;
      if (!styleTag) {
        styleTag = iframe.contentDocument.createElement('style');
        styleTag.id = 'theme-style';
        iframe.contentDocument.head.appendChild(styleTag);
      }
      styleTag.textContent = generateStyleInjection();
    }
  }, [theme, sanitizedHtml]);

  if (!sanitizedHtml) return <div>Loading Preview...</div>;

  return (
    <div className="h-full w-full bg-white">
      <iframe
        ref={iframeRef}
        className="w-full h-full border rounded-lg shadow-xl"
        srcDoc={sanitizedHtml}
        sandbox="allow-same-origin allow-scripts"
        onLoad={() => {
          const iframe = iframeRef.current;
          if (iframe && iframe.contentDocument) {
            let styleTag = iframe.contentDocument.getElementById('theme-style') as HTMLStyleElement | null;
            if (!styleTag) {
              styleTag = iframe.contentDocument.createElement('style');
              styleTag.id = 'theme-style';
              iframe.contentDocument.head.appendChild(styleTag);
            }
            styleTag.textContent = generateStyleInjection();
          }
        }}
      />
    </div>
  );
};

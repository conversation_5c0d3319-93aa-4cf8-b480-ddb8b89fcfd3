import { ThemeSettings } from "./types";

export const FONT_OPTIONS = [
  { label: 'Roboto', value: 'Roboto', category: 'sans-serif' },
  { label: 'Playfair Display', value: 'Playfair Display', category: 'serif' },
  { label: 'Mont<PERSON><PERSON>', value: 'Montserrat', category: 'sans-serif' },
  { label: 'Lora', value: 'Lora', category: 'serif' },
  { label: 'Open Sans', value: 'Open Sans', category: 'sans-serif' },
  { label: 'Poppins', value: 'Poppins', category: 'sans-serif' },
  { label: 'Merriweather', value: 'Merriweather', category: 'serif' },
  { label: '<PERSON>lew<PERSON>', value: '<PERSON>lew<PERSON>', category: 'sans-serif' },
  { label: '<PERSON>', value: '<PERSON>', category: 'sans-serif' },
  { label: 'Ubuntu', value: 'Ubuntu', category: 'sans-serif' },
  { label: 'Source Sans Pro', value: 'Source Sans Pro', category: 'sans-serif' },
];

export const DEFAULT_THEME = {
  bg: 'bg-gray-900',
  name: 'Default',
  colors: ['#6b1d1d', '#4f46e5', '#ffffff', '#111827', '#111827'],
  primaryColor: '#6b1d1d',
  secondaryColor: '#4f46e5',
  backgroundColor: '#ffffff',
  textColor: '#111827',
  headingColor: '#111827',
  headingFont: 'Playfair Display',
  bodyFont: 'Roboto',
};
// //without responsive design
// 'use client';

// import React from 'react';
// import { Wand2, LayoutTemplate, ArrowRight } from 'lucide-react';
// import { useRouter } from 'next/navigation'; // assuming Next.js app router

// type BuilderChoiceProps = {
//   onSelectAI: () => void;
//   onSelectClassic: () => void;
// };

// export default function BuilderChoice({ onSelectAI, onSelectClassic }: BuilderChoiceProps) {
//   const router = useRouter();

//   return (
//     <div className="flex flex-col items-center justify-center h-full px-4 text-center">
//       <h2 className="mb-12 text-2xl font-semibold sm:text-3xl md:text-4xl">
//         How would you like to build your website?
//       </h2>

//       <div className="flex flex-col max-w-4xl gap-6 mb-10 sm:flex-row">
//         {/* AI Website Builder */}
//         <div
//           onClick={onSelectAI}
//           className="cursor-pointer w-full sm:w-1/2 bg-white border border-[#C5DB9D] transition-all duration-300 rounded-2xl p-6 text-left relative hover:shadow-[0_0_25px_#C5DB9D] hover:scale-[1.02] hover:brightness-105 focus:outline focus:ring-4 focus:ring-[#C5DB9D]/50"
//         >
//           <div className="flex items-center mb-4 space-x-3">
//             <Wand2 className="text-[#6D6D81] w-6 h-6" />
//             <h3 className="text-lg font-bold">AI Website Builder</h3>
//           </div>
//           <p className="mb-4 text-sm text-gray-600">
//             Experience the future of website building. Build your website by answering some questions.
//           </p>
//           <button className="mt-auto bg-[#7DBE1D] hover:bg-[#6EAD1A] text-white px-4 py-2 rounded text-sm font-medium flex items-center space-x-1 transition-all duration-300">
//             <span>Build with AI</span>
//             <ArrowRight className="w-4 h-4" />
//           </button>
//         </div>

//         {/* Classic Templates */}
//         <div
//           onClick={onSelectClassic}
//           className="cursor-pointer w-full sm:w-1/2 bg-white border border-transparent hover:border-[#7DBE1D] transition-all duration-300 rounded-2xl p-6 text-left relative hover:shadow-[0_0_25px_#7DBE1D] hover:scale-[1.02] hover:brightness-105 focus:outline focus:ring-4 focus:ring-[#C5DB9D]/50"
//         >
//           <div className="flex items-center mb-4 space-x-3">
//             <LayoutTemplate className="text-[#7DBE1D] w-6 h-6" />
//             <h3 className="text-lg font-bold">Use Custom Templates</h3>
//           </div>
//           <p className="mb-4 text-sm text-gray-600">
//             Begin with our professionally designed starter templates tailored to meet your requirements.
//           </p>
//           <button className="flex items-center px-4 py-2 mt-auto space-x-1 text-sm font-medium text-gray-800 transition-all duration-300 bg-gray-200 rounded hover:bg-gray-300">
//             <span>Build with Templates</span>
//             <ArrowRight className="w-4 h-4" />
//           </button>
//         </div>
//       </div>

//       {/* Back Link */}
//       <button
//         onClick={() => router.push('/')}
//         className="text-sm text-gray-500 transition hover:text-gray-700"
//       >
//         &larr; Go Back
//       </button>
//     </div>
//   );
// }



//with responsive design

'use client';

import React from 'react';
import { ArrowRight, LayoutTemplate, Wand2 } from 'lucide-react';
import { useRouter } from 'next/navigation'; // assuming Next.js app router

type BuilderChoiceProps = {
  onSelectClassic: () => void;
};

export default function BuilderChoice({ onSelectClassic }: BuilderChoiceProps) {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center h-full px-4 py-8 text-center sm:py-12">
      <h2 className="mb-6 text-xl font-semibold sm:text-2xl md:text-3xl sm:mb-12">
        How would you like to build your website?
      </h2>

      <div className="flex flex-col w-full max-w-4xl gap-4 mb-6 sm:flex-row sm:gap-6 sm:mb-10">
        {/* AI Website Builder */}
        <div
          onClick={() => router.push('/assistant')}
          className="cursor-pointer w-full sm:w-1/2 bg-white border border-[#C5DB9D] transition-all duration-300 rounded-2xl p-4 sm:p-6 text-left relative hover:shadow-[0_0_25px_#C5DB9D] hover:scale-[1.02] hover:brightness-105 focus:outline focus:ring-4 focus:ring-[#C5DB9D]/50"
        >
          <div className="flex items-center mb-3 space-x-2 sm:space-x-3 sm:mb-4">
            <Wand2 className="text-[#6D6D81] w-5 h-5 sm:w-6 sm:h-6" />
            <h3 className="text-base font-bold sm:text-lg">AI Website Builder</h3>
          </div>
          <p className="mb-3 text-xs text-gray-600 sm:text-sm sm:mb-4">
            Experience the future of website building. Build your website by answering some questions.
          </p>
          <button className="mt-auto bg-[#7DBE1D] hover:bg-[#6EAD1A] text-white px-3 py-2 rounded text-xs sm:text-sm font-medium flex items-center space-x-1 transition-all duration-300">
            <span>Build with AI</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

        {/* Classic Templates */}
        <div
          onClick={onSelectClassic}
          className="cursor-pointer w-full sm:w-1/2 bg-white border border-transparent hover:border-[#7DBE1D] transition-all duration-300 rounded-2xl p-4 sm:p-6 text-left relative hover:shadow-[0_0_25px_#7DBE1D] hover:scale-[1.02] hover:brightness-105 focus:outline focus:ring-4 focus:ring-[#C5DB9D]/50"
        >
          <div className="flex items-center mb-3 space-x-2 sm:space-x-3 sm:mb-4">
            <LayoutTemplate className="text-[#7DBE1D] w-5 h-5 sm:w-6 sm:h-6" />
            <h3 className="text-base font-bold sm:text-lg">Use Custom Templates</h3>
          </div>
          <p className="mb-3 text-xs text-gray-600 sm:text-sm sm:mb-4">
            Begin with our professionally designed starter templates tailored to meet your requirements.
          </p>
          <button className="flex items-center px-3 py-2 mt-auto space-x-1 text-xs font-medium text-gray-800 transition-all duration-300 bg-gray-200 rounded hover:bg-gray-300 sm:text-sm">
            <span>Build with Templates</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Back Link */}
      <button
        onClick={() => router.push('/')} 
        className="text-xs text-gray-500 transition sm:text-sm hover:text-gray-700"
      >
        &larr; Go Back
      </button>
    </div>
  );
}

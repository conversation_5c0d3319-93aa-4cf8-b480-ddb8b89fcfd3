
// 'use client';

// import React from 'react';
// import { Input } from '@/components/ui/input';
// import { useSiteBuilder } from 'src/context/SiteBuilderContext';

// export const WebsiteInfo: React.FC = () => {
//   const { formData, updateFormData, errors, setErrors } = useSiteBuilder();

//   const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     updateFormData({ name: e.target.value });
//     if (e.target.value.trim()) {
//       setErrors(prev => ({ ...prev, name: '' }));
//     }
//   };

//   const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//     updateFormData({ category: e.target.value });
//     if (e.target.value) {
//       setErrors(prev => ({ ...prev, category: '' }));
//     }
//   };

//   return (
//     <div className="space-y-6">
//       <div>
//         <h2 className="text-xl font-semibold mb-2">Website Info</h2>
//         <h3 className="text-lg font-medium">Let's build your website!</h3>
//         <p className="text-muted-foreground text-sm mt-1">
//           Please share some basic details of the website to get started.
//         </p>
//       </div>

//       <div className="space-y-4">
//         <div className="space-y-2">
//           <label className="text-sm font-medium">
//             Name of the website: <span className="text-red-500">*</span>
//           </label>
//           <Input
//             placeholder="Enter website name"
//             value={formData.name}
//             onChange={handleNameChange}
//             className={errors.name ? 'border-red-500 focus-visible:ring-red-500' : ''}
//           />
//           {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
//         </div>

//         <div className="space-y-2">
//           <label className="text-sm font-medium">
//             This website is for: <span className="text-red-500">*</span>
//           </label>
//           <select
//             value={formData.category}
//             onChange={handleCategoryChange}
//             className={`flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 ${
//               errors.category
//                 ? 'border-red-500 focus-visible:ring-red-500'
//                 : 'border-input focus-visible:ring-ring'
//             }`}
//           >
//             <option value="">Select a category</option>
//             <option value="Business">Business</option>
//             <option value="Portfolio">Portfolio</option>
//             <option value="Blog">Blog</option>
//             <option value="E-commerce">E-commerce</option>
//             <option value="Nonprofit">Nonprofit</option>
//             <option value="Personal">Personal</option>
//             <option value="Restaurant">Restaurant</option>
//             <option value="Gym">Gym</option>
//             <option value="Other">Other</option>
//           </select>
//           {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
//         </div>
//       </div>
//     </div>
//   );
// };





'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { useSiteBuilder } from 'src/context/SiteBuilderContext';

export const WebsiteInfo: React.FC = () => {
  const { formData, updateFormData, errors, setErrors } = useSiteBuilder();

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFormData({ name: e.target.value });
    if (e.target.value.trim()) {
      setErrors(prev => ({ ...prev, name: '' }));
    }
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateFormData({ category: e.target.value });
    if (e.target.value) {
      setErrors(prev => ({ ...prev, category: '' }));
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Website Info</h2>
        <h3 className="text-lg font-medium">Let's build your website!</h3>
        <p className="text-muted-foreground text-sm mt-1">
          Please share some basic details of the website to get started.
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Name of the website: <span className="text-red-500">*</span>
          </label>
          <Input
            placeholder="Enter website name"
            value={formData.name}
            onChange={handleNameChange}
            className={errors.name ? 'border-red-500 focus-visible:ring-red-500' : ''}
          />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">
            This website is for: <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <select
              value={formData.category}
              onChange={handleCategoryChange}
              className={`flex h-10 w-full appearance-none rounded-md border bg-white px-3 py-2 pr-8 text-sm shadow-sm focus:outline-none focus:ring-1 ${
                errors.category
                  ? 'border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
            >
              <option value="">Select a category</option>
              <option value="Business">Business</option>
              <option value="Portfolio">Portfolio</option>
              <option value="Blog">Blog</option>
              <option value="E-commerce">E-commerce</option>
              <option value="Nonprofit">Nonprofit</option>
              <option value="Personal">Personal</option>
              <option value="Restaurant">Restaurant</option>
              <option value="Gym">Gym</option>
              <option value="Other">Other</option>
            </select>
          </div>
          {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
        </div>
      </div>
    </div>
  );
};

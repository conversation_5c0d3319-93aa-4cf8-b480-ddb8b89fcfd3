// 'use client';

// import React, { useState } from 'react';
// import { useSiteBuilder } from 'src/context/SiteBuilderContext';
// import { CustomizationProvider } from 'src/components/customization'; // Only import the provider here
// import ThemeControlPanel from 'src/components/customization/ThemeControlPanel';
// import StylesPanel from 'src/components/customization/StylesPanel';
// import ImagePanel from 'src/components/customization/ImagePanel';
// import TemplatePreview from 'src/components/TemplatePreview2';
// import { AnimatePresence, motion } from 'framer-motion';
// import StylesPanelWrapper from 'src/components/customization/StylesPanelWrapper';

// const API_BASE = process.env.NEXT_PUBLIC_WORDPRESS_API!;

// export const Customization: React.FC = () => {
//   const { formData, nextStep, prevStep } = useSiteBuilder();
//   const [showStyles, setShowStyles] = useState(false);
//   const [showImages, setShowImages] = useState(false);

//   // NOTE: Don't use useCustomization() here! It's only valid inside the provider.

//   const [previewUrl, setPreviewUrl] = useState('');

//   // We'll get theme from ThemeControlPanel via useCustomization, so handleNext must be passed down
//   // We'll pass handleNext as a function that receives theme as an argument

//   const handleNext = async (theme: any) => {
//     const finalSiteData = {
//       designId: formData.selectedTemplate, // or selectedSitePath if stored in context
//       primaryColor: theme.primaryColor,
//       secondaryColor: theme.secondaryColor,
//       accentColor: theme.accentColor,
//       headingFont: theme.headingFont,
//       bodyFont: theme.bodyFont,
//       siteName: formData.name,
//       category: formData.category,
//       description: formData.description,
//       heroImage: formData.images[0]?.url || '',
//       bigTitleImage: formData.images[1]?.url || '',
//       logoUrl: theme.logoUrl || '',
//       faviconUrl: theme.faviconUrl || '',
//     };

//     localStorage.setItem('finalSiteData', JSON.stringify(finalSiteData));

//     try {
//       await fetch(`${API_BASE}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify(theme),
//       });

//       const previewKey = formData.previewKey; // must be stored in context previously
//       const fullPreviewUrl = `${formData.selectedSitePath}/?preview_key=${previewKey}&heroImageUrl=${encodeURIComponent(formData.images[0]?.url || '')}&headerImageUrl=${encodeURIComponent(formData.images[1]?.url || '')}&logoUrl=${encodeURIComponent(theme.logoUrl || '')}&_refresh=${Date.now()}`;

//       setPreviewUrl(fullPreviewUrl);
//       nextStep();
//     } catch (error) {
//       console.error('Error updating theme settings or generating preview:', error);
//     }
//   };

//   const handleBack = () => {
//     prevStep();
//   };

//   const previewPath = formData.selectedSitePath && formData.previewKey
//     ? `${formData.selectedSitePath}/?preview_key=${formData.previewKey}&heroImageUrl=${encodeURIComponent(formData.images[0]?.url || '')}&headerImageUrl=${encodeURIComponent(formData.images[1]?.url || '')}`
//     : '';

//   return (
//     <CustomizationProvider>
//       <motion.div className="fixed inset-0 flex bg-white overflow-hidden" layout>
//         {/* Left Sidebar */}
//         <ThemeControlPanel
//           sitePath={formData.selectedSitePath || ''}
//           onNext={handleNext}
//           onBack={handleBack}
//           showStyles={showStyles}
//           setShowStyles={setShowStyles}
//           showImages={showImages}
//           setShowImages={setShowImages}
//         />

//         {/* Styles Panel with animation and layout */}
//         <AnimatePresence initial={false}>
//           {showStyles && (
//             <motion.div
//               key="styles-panel"
//               layout
//               initial={{ width: 0, opacity: 0 }}
//               animate={{ width: '18rem', opacity: 1 }}
//               exit={{ width: 0, opacity: 0 }}
//               transition={{ duration: 0.4, ease: 'easeInOut' }}
//               className="overflow-hidden"
//             >
//               <StylesPanelWrapper />
//             </motion.div>
//           )}
//           {showImages && (
//           <motion.div
//             key="image-panel"
//             layout
//             initial={{ width: 0, opacity: 0 }}
//             animate={{ width: '18rem', opacity: 1 }}
//             exit={{ width: 0, opacity: 0 }}
//             transition={{ duration: 0.4, ease: 'easeInOut' }}
//             className="overflow-hidden"
//           >
//             <ImagePanel />
//           </motion.div>
//         )}
//         </AnimatePresence>

//         {/* Live Preview with border and info */}
//         <motion.div
//           className="flex-1 transition-all duration-300 px-6 py-6 overflow-auto"
//           layout
//         >
//           {/* Info Text */}
//           <div className="mb-4 text-sm text-gray-600 bg-white rounded-md shadow-sm px-4 py-2 border border-gray-200 text-center">
//             <strong>This is just a sneak peek.</strong> Actual website will be created in later stages.
//           </div>

//           {/* Preview Wrapper with Border */}
//           <div className="border border-white rounded-md overflow-hidden shadow-md h-[calc(100vh-8rem)]">
//             <TemplatePreview sitePath={previewPath} />
//           </div>
//         </motion.div>

//       </motion.div>
//     </CustomizationProvider>
//   );
// };



//image mapping is working when hardoded, but not when dynamic
// 'use client';

// import React, { useState } from 'react';
// import { useSiteBuilder } from 'src/context/SiteBuilderContext';
// import { CustomizationProvider } from 'src/components/customization';
// import ThemeControlPanel from 'src/components/customization/ThemeControlPanel';
// import StylesPanelWrapper from 'src/components/customization/StylesPanelWrapper';
// import ImagePanel from 'src/components/customization/ImagePanel';
// import TemplatePreview from 'src/components/TemplatePreview2';
// import { AnimatePresence, motion } from 'framer-motion';
// import { FiChevronRight } from 'react-icons/fi';

// const API_BASE = process.env.NEXT_PUBLIC_WORDPRESS_API!;

// export const Customization: React.FC = () => {
//   const { formData, nextStep, prevStep } = useSiteBuilder();
//   const [showStyles, setShowStyles] = useState(false);
//   const [showImages, setShowImages] = useState(false);
//   const [sidebarVisible, setSidebarVisible] = useState(true);
//   const [device, setDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

//   const handleNext = async (theme: any) => {
//     const finalSiteData = {
//       designId: formData.selectedTemplate,
//       primaryColor: theme.primaryColor,
//       secondaryColor: theme.secondaryColor,
//       accentColor: theme.accentColor,
//       headingFont: theme.headingFont,
//       bodyFont: theme.bodyFont,
//       siteName: formData.name,
//       category: formData.category,
//       description: formData.description,
//       heroImage: formData.images[0]?.url || '',
//       bigTitleImage: formData.images[1]?.url || '',
//       logoUrl: theme.logoUrl || '',
//       faviconUrl: theme.faviconUrl || '',
//     };

//     localStorage.setItem('finalSiteData', JSON.stringify(finalSiteData));

//     try {
//       await fetch(`${API_BASE}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify(theme),
//       });

//       const previewKey = formData.previewKey;
//       const fullPreviewUrl = `${formData.selectedSitePath}/?preview_key=${previewKey}&heroImageUrl=${encodeURIComponent(formData.images[0]?.url || '')}&headerImageUrl=${encodeURIComponent(formData.images[1]?.url || '')}&logoUrl=${encodeURIComponent(theme.logoUrl || '')}&_refresh=${Date.now()}`;

//       nextStep();
//     } catch (error) {
//       console.error('Error updating theme settings or generating preview:', error);
//     }
//   };

//   const previewPath = formData.selectedSitePath && formData.previewKey
//     ? (() => {
//         const url = new URL(`${formData.selectedSitePath}/`, window.location.origin);
//         url.searchParams.set('preview_key', formData.previewKey);
//         url.searchParams.set('_refresh', Date.now().toString());

//         // Attach all selected images by hardcoded keys (based on expected image replacements)
//         if (formData.images?.[0]) url.searchParams.set('home-image1', formData.images[0].url);
//         if (formData.images?.[1]) url.searchParams.set('home-image2', formData.images[1].url);
//         if (formData.images?.[2]) url.searchParams.set('home-image3', formData.images[2].url);
//         if (formData.images?.[3]) url.searchParams.set('home-image4', formData.images[3].url);
//         if (formData.images?.[4]) url.searchParams.set('home-image5', formData.images[4].url);
//         if (formData.images?.[5]) url.searchParams.set('home-image6', formData.images[5].url);
//         if (formData.images?.[6]) url.searchParams.set('home-image7', formData.images[6].url);
//         if (formData.images?.[7]) url.searchParams.set('home-image8', formData.images[7].url);
//         if (formData.images?.[8]) url.searchParams.set('home-image9', formData.images[8].url);
//         if (formData.images?.[9]) url.searchParams.set('home-image10', formData.images[9].url);
//         if (formData.images?.[10]) url.searchParams.set('home-image11', formData.images[10].url);


//         // Attach all selected images
//         formData.images?.forEach((img, index) => {
//           url.searchParams.set(`img${index}`, encodeURIComponent(img.url));
//         });

//         return url.toString();
//       })()
//     : '';

//   return (
//     <CustomizationProvider>
//       <motion.div className="fixed inset-0 flex bg-white overflow-hidden" layout>
//         {sidebarVisible && (
//         <ThemeControlPanel
//           sitePath={formData.selectedSitePath || ''}
//           onNext={handleNext}
//           onBack={prevStep}
//           showStyles={showStyles}
//           setShowStyles={setShowStyles}
//           showImages={showImages}
//           setShowImages={setShowImages}
//           toggleSidebar={() => setSidebarVisible(false)}
//           device={device}
//           setDevice={setDevice}
//         />
//         )}

//         {!sidebarVisible && (
//           <button
//             onClick={() => setSidebarVisible(true)}
//             className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white border rounded-r px-2 py-1 shadow z-50"
//           >
//             <FiChevronRight />
//           </button>
//         )}

//         <AnimatePresence initial={false}>
//           {showStyles && (
//             <motion.div
//               key="styles-panel"
//               layout
//               initial={{ width: 0, opacity: 0 }}
//               animate={{ width: '18rem', opacity: 1 }}
//               exit={{ width: 0, opacity: 0 }}
//               transition={{ duration: 0.4 }}
//               className="overflow-hidden"
//             >
//               <StylesPanelWrapper />
//             </motion.div>
//           )}
//           {showImages && (
//             <motion.div
//               key="image-panel"
//               layout
//               initial={{ width: 0, opacity: 0 }}
//               animate={{ width: '18rem', opacity: 1 }}
//               exit={{ width: 0, opacity: 0 }}
//               transition={{ duration: 0.4 }}
//               className="overflow-hidden"
//             >
//               <ImagePanel />
//             </motion.div>
//           )}
//         </AnimatePresence>

//         <motion.div className="flex-1 transition-all duration-300 px-6 py-6 overflow-auto" layout>
//           <div className="mb-4 text-sm text-gray-600 bg-white rounded-md shadow-sm px-4 py-2 border border-gray-200 text-center">
//             <strong>This is just a sneak peek.</strong> Actual website will be created in later stages.
//           </div>

//           {/* Responsive Preview Area */}
//           <div className="flex justify-center items-start h-[calc(100vh-10rem)] overflow-hidden">
//             <TemplatePreview sitePath={previewPath} device={device} />
//           </div>
//         </motion.div>
//       </motion.div>
//     </CustomizationProvider>
//   );
// };

//above is working

//looping to repeat the images

// 'use client';

// import React, { useState } from 'react';
// import { useSiteBuilder } from 'src/context/SiteBuilderContext';
// import { CustomizationProvider } from 'src/components/customization';
// import ThemeControlPanel from 'src/components/customization/ThemeControlPanel';
// import StylesPanelWrapper from 'src/components/customization/StylesPanelWrapper';
// import ImagePanel from 'src/components/customization/ImagePanel';
// import TemplatePreview from 'src/components/TemplatePreview2';
// import { AnimatePresence, motion } from 'framer-motion';
// import { FiChevronRight } from 'react-icons/fi';

// const API_BASE = process.env.NEXT_PUBLIC_WORDPRESS_API!;

// export const Customization: React.FC = () => {
//   const { formData, nextStep, prevStep } = useSiteBuilder();
//   const [showStyles, setShowStyles] = useState(false);
//   const [showImages, setShowImages] = useState(false);
//   const [sidebarVisible, setSidebarVisible] = useState(true);
//   const [device, setDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

//   const handleNext = async (theme: any) => {
//     const finalSiteData = {
//       designId: formData.selectedTemplate,
//       primaryColor: theme.primaryColor,
//       secondaryColor: theme.secondaryColor,
//       accentColor: theme.accentColor,
//       headingFont: theme.headingFont,
//       bodyFont: theme.bodyFont,
//       siteName: formData.name,
//       category: formData.category,
//       description: formData.description,
//       heroImage: formData.images[0]?.url || '',
//       bigTitleImage: formData.images[1]?.url || '',
//       logoUrl: theme.logoUrl || '',
//       faviconUrl: theme.faviconUrl || '',
//     };

//     localStorage.setItem('finalSiteData', JSON.stringify(finalSiteData));

//     try {
//       await fetch(`${API_BASE}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify(theme),
//       });

//       const previewKey = formData.previewKey;
//       const fullPreviewUrl = `${formData.selectedSitePath}/?preview_key=${previewKey}&heroImageUrl=${encodeURIComponent(formData.images[0]?.url || '')}&headerImageUrl=${encodeURIComponent(formData.images[1]?.url || '')}&logoUrl=${encodeURIComponent(theme.logoUrl || '')}&_refresh=${Date.now()}`;

//       nextStep();
//     } catch (error) {
//       console.error('Error updating theme settings or generating preview:', error);
//     }
//   };

//   // Update the previewPath logic in Customization.tsx
//   const previewPath = formData.selectedSitePath && formData.previewKey
//     ? (() => {
//         const url = new URL(`${formData.selectedSitePath}/`, window.location.origin);
//         url.searchParams.set('preview_key', formData.previewKey);
//         url.searchParams.set('_refresh', Date.now().toString());

//         // Use the selectedImages array directly to maintain order
//         formData.selectedImages?.forEach((img, index) => {
//           url.searchParams.set(`home-image${index + 1}`, img);
//         });

//         // Optional: Send all images too, like before
//         formData.images?.forEach((img, index) => {
//           url.searchParams.set(`img${index}`, encodeURIComponent(img.url));
//         });

//         return url.toString();
//       })()
//     : '';

//   return (
//     <CustomizationProvider>
//       <motion.div className="fixed inset-0 flex bg-white overflow-hidden" layout>
//         {sidebarVisible && (
//         <ThemeControlPanel
//           sitePath={formData.selectedSitePath || ''}
//           onNext={handleNext}
//           onBack={prevStep}
//           showStyles={showStyles}
//           setShowStyles={setShowStyles}
//           showImages={showImages}
//           setShowImages={setShowImages}
//           toggleSidebar={() => setSidebarVisible(false)}
//           device={device}
//           setDevice={setDevice}
//         />
//         )}

//         {!sidebarVisible && (
//           <button
//             onClick={() => setSidebarVisible(true)}
//             className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white border rounded-r px-2 py-1 shadow z-50"
//           >
//             <FiChevronRight />
//           </button>
//         )}

//         <AnimatePresence initial={false}>
//           {showStyles && (
//             <motion.div
//               key="styles-panel"
//               layout
//               initial={{ width: 0, opacity: 0 }}
//               animate={{ width: '18rem', opacity: 1 }}
//               exit={{ width: 0, opacity: 0 }}
//               transition={{ duration: 0.4 }}
//               className="overflow-hidden"
//             >
//               <StylesPanelWrapper />
//             </motion.div>
//           )}
//           {showImages && (
//             <motion.div
//               key="image-panel"
//               layout
//               initial={{ width: 0, opacity: 0 }}
//               animate={{ width: '18rem', opacity: 1 }}
//               exit={{ width: 0, opacity: 0 }}
//               transition={{ duration: 0.4 }}
//               className="overflow-hidden"
//             >
//               <ImagePanel />
//             </motion.div>
//           )}
//         </AnimatePresence>

//         <motion.div className="flex-1 transition-all duration-300 px-6 py-6 overflow-auto" layout>
//           <div className="mb-4 text-sm text-gray-600 bg-white rounded-md shadow-sm px-4 py-2 border border-gray-200 text-center">
//             <strong>This is just a sneak peek.</strong> Actual website will be created in later stages.
//           </div>

//           {/* Responsive Preview Area */}
//           <div className="flex justify-center items-start h-[calc(100vh-10rem)] overflow-hidden">
//             <TemplatePreview sitePath={previewPath} device={device} />
//           </div>
//         </motion.div>
//       </motion.div>
//     </CustomizationProvider>
//   );
// };




'use client';

import React, { useState, useEffect } from 'react';
import { useSiteBuilder } from 'src/context/SiteBuilderContext';
import { CustomizationProvider } from 'src/components/customization';
import ThemeControlPanel from 'src/components/customization/ThemeControlPanel';
import StylesPanelWrapper from 'src/components/customization/StylesPanelWrapper';
import ImagePanel from 'src/components/customization/ImagePanel';
import TemplatePreview from 'src/components/TemplatePreview2';
import { AnimatePresence, motion } from 'framer-motion';
import { FiChevronRight } from 'react-icons/fi';

const API_BASE = process.env.NEXT_PUBLIC_WORDPRESS_API!;

export const Customization: React.FC = () => {
  const { formData, nextStep, prevStep } = useSiteBuilder();
  const [showStyles, setShowStyles] = useState(false);
  const [showImages, setShowImages] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true);
  const [device, setDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const handleNext = async (theme: any) => {
    const finalSiteData = {
      designId: formData.selectedTemplate,
      primaryColor: theme.primaryColor,
      secondaryColor: theme.secondaryColor,
      accentColor: theme.accentColor,
      headingFont: theme.headingFont,
      bodyFont: theme.bodyFont,
      siteName: formData.name,
      category: formData.category,
      description: formData.description,
      heroImage: formData.images[0]?.url || '',
      bigTitleImage: formData.images[1]?.url || '',
      logoUrl: theme.logoUrl || '',
      faviconUrl: theme.faviconUrl || '',
    };

    localStorage.setItem('finalSiteData', JSON.stringify(finalSiteData));

    try {
      await fetch(`${API_BASE}/wp-json/custom/v1/update-theme-settings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(theme),
      });

      const previewKey = formData.previewKey;
      const fullPreviewUrl = `${formData.selectedSitePath}/?preview_key=${previewKey}&heroImageUrl=${encodeURIComponent(formData.images[0]?.url || '')}&headerImageUrl=${encodeURIComponent(formData.images[1]?.url || '')}&logoUrl=${encodeURIComponent(theme.logoUrl || '')}&_refresh=${Date.now()}`;

      nextStep();
    } catch (error) {
      console.error('Error updating theme settings or generating preview:', error);
    }
  };

  // Construct preview URL with query params
  const previewPath = formData.selectedSitePath && formData.previewKey
    ? (() => {
        const url = new URL(`${formData.selectedSitePath}/`, window.location.origin);
        url.searchParams.set('preview_key', formData.previewKey);
        url.searchParams.set('_refresh', Date.now().toString());

        formData.selectedImages?.forEach((img, index) => {
          url.searchParams.set(`home-image${index + 1}`, img);
        });

        formData.images?.forEach((img, index) => {
          url.searchParams.set(`img${index}`, encodeURIComponent(img.url));
        });

        return url.toString();
      })()
    : '';

  // Mobile detection and sidebar behavior
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setSidebarVisible(false);
      } else {
        setSidebarVisible(true);
      }
    };

    handleResize(); // On mount
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <CustomizationProvider>
      <motion.div className="fixed inset-0 flex bg-white overflow-hidden" layout>
        {/* MOBILE VIEW */}
        {isMobile ? (
          sidebarVisible ? (
            <div className="w-full h-full">
              <ThemeControlPanel
                sitePath={formData.selectedSitePath || ''}
                onNext={handleNext}
                onBack={prevStep}
                showStyles={showStyles}
                setShowStyles={setShowStyles}
                showImages={showImages}
                setShowImages={setShowImages}
                toggleSidebar={() => setSidebarVisible(false)}
                device={device}
                setDevice={setDevice}
              />
            </div>
          ) : (
            <>
              <div className="flex-1 px-2 py-4">
                <div className="text-sm text-gray-600 text-center mb-2 px-4">
                  <strong>This is just a sneak peek.</strong> Actual website will be created in later stages.
                </div>
                <div className="flex justify-center items-start h-[calc(100vh-8rem)] overflow-hidden">
                  <TemplatePreview sitePath={previewPath} device={device} />
                </div>
              </div>
              <button
                onClick={() => setSidebarVisible(true)}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white border rounded-r px-2 py-1 shadow z-50"
                title="Open panel"
              >
                <FiChevronRight />
              </button>
            </>
          )
        ) : (
          // DESKTOP/TABLET VIEW
          <>
            {sidebarVisible && (
              <ThemeControlPanel
                sitePath={formData.selectedSitePath || ''}
                onNext={handleNext}
                onBack={prevStep}
                showStyles={showStyles}
                setShowStyles={setShowStyles}
                showImages={showImages}
                setShowImages={setShowImages}
                toggleSidebar={() => setSidebarVisible(false)}
                device={device}
                setDevice={setDevice}
              />
            )}

            {!sidebarVisible && (
              <button
                onClick={() => setSidebarVisible(true)}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white border rounded-r px-2 py-1 shadow z-50"
                title="Open panel"
              >
                <FiChevronRight />
              </button>
            )}

            <AnimatePresence initial={false}>
              {showStyles && (
                <motion.div
                  key="styles-panel"
                  layout
                  initial={{ width: 0, opacity: 0 }}
                  animate={{ width: '18rem', opacity: 1 }}
                  exit={{ width: 0, opacity: 0 }}
                  transition={{ duration: 0.4 }}
                  className="overflow-hidden"
                >
                  <StylesPanelWrapper />
                </motion.div>
              )}
              {showImages && (
                <motion.div
                  key="image-panel"
                  layout
                  initial={{ width: 0, opacity: 0 }}
                  animate={{ width: '18rem', opacity: 1 }}
                  exit={{ width: 0, opacity: 0 }}
                  transition={{ duration: 0.4 }}
                  className="overflow-hidden"
                >
                  <ImagePanel />
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div className="flex-1 transition-all duration-300 px-6 py-6 overflow-auto" layout>
              <div className="mb-4 text-sm text-gray-600 bg-white rounded-md shadow-sm px-4 py-2 border border-gray-200 text-center">
                <strong>This is just a sneak peek.</strong> Actual website will be created in later stages.
              </div>

              <div className="flex justify-center items-start h-[calc(100vh-10rem)] overflow-hidden">
                <TemplatePreview sitePath={previewPath} device={device} />
              </div>
            </motion.div>
          </>
        )}
      </motion.div>
    </CustomizationProvider>
  );
};

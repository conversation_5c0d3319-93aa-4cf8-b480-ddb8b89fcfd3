
// 'use client';

// import { useState } from 'react';
// import { Button } from '@/components/ui/button';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';

// export const WebsiteDescription = () => {
//   const { formData, updateFormData, errors, setErrors } = useSiteBuilder();
//   const [isImproving, setIsImproving] = useState(false);
//   const [error, setError] = useState('');

//   const handleImproveBrief = async () => {
//     const brief = formData.description;

//     if (!brief.trim()) {
//       setErrors(prev => ({ ...prev, description: 'Please describe your website' }));
//       return;
//     }

//     setIsImproving(true);
//     setError('');
//     setErrors(prev => ({ ...prev, description: '' }));

//     try {
//       const response = await fetch('/api/improve-brief', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ brief }),
//       });

//       if (!response.ok) throw new Error('Failed to improve brief');

//       const { improvedBrief } = await response.json();
//       updateFormData({ description: improvedBrief });
//     } catch (err) {
//       console.error('Brief improvement error:', err);
//       setError('Failed to improve brief. Please try again.');
//     } finally {
//       setIsImproving(false);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       <div>
//         <h2 className="text-xl font-semibold mb-2">Website Brief</h2>
//         <p className="text-muted-foreground text-sm">
//           Describe your website in detail. We'll use this to generate content and design suggestions.
//         </p>
//       </div>

//       <div className="space-y-4">
//         <textarea
//           value={formData.description}
//           onChange={(e) => {
//             updateFormData({ description: e.target.value });
//             if (e.target.value.trim()) {
//               setErrors(prev => ({ ...prev, description: '' }));
//             }
//           }}
//           placeholder="Example: I need a modern e-commerce site for handmade ceramics with an earthy color scheme..."
//           className={`w-full h-40 p-3 border rounded-md text-sm focus:ring-2 transition ${
//             errors.description ? 'border-red-500 focus:ring-red-500' : 'border-input focus:ring-blue-500'
//           }`}
//           maxLength={3000}
//         />
//         {errors.description && (
//           <p className="text-sm text-red-500">{errors.description}</p>
//         )}

//         <div className="flex items-center gap-2">
//           <Button 
//             onClick={handleImproveBrief}
//             disabled={!formData.description || isImproving}
//           >
//             {isImproving ? (
//               <>
//                 <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
//                   <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                   <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//                 </svg>
//                 Improving...
//               </>
//             ) : (
//               'Improve with AI ✨'
//             )}
//           </Button>

//           <span className="text-sm text-muted-foreground">
//             {formData.description.length}/3000 characters
//           </span>
//         </div>

//         {error && (
//           <div className="text-red-500 text-sm">{error}</div>
//         )}
//       </div>
//     </div>
//   );
// };




'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSiteBuilder } from '@/context/SiteBuilderContext';

export const WebsiteDescription = () => {
  const { formData, updateFormData, errors, setErrors } = useSiteBuilder();
  const [isImproving, setIsImproving] = useState(false);
  const [error, setError] = useState('');

  const handleImproveBrief = async () => {
    const brief = formData.description;

    if (!brief.trim()) {
      setErrors(prev => ({ ...prev, description: 'Please describe your website' }));
      return;
    }

    setIsImproving(true);
    setError('');
    setErrors(prev => ({ ...prev, description: '' }));

    try {
      const response = await fetch('/api/improve-brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ brief }),
      });

      if (!response.ok) throw new Error('Failed to improve brief');

      const { improvedBrief } = await response.json();
      updateFormData({ description: improvedBrief });
    } catch (err) {
      console.error('Brief improvement error:', err);
      setError('Failed to improve brief. Please try again.');
    } finally {
      setIsImproving(false);
    }
  };

  return (
    <div className="space-y-6 text-sm overflow-x-hidden">
      <div>
        <h2 className="text-lg font-semibold mb-2">Website Brief</h2>
        <p className="text-muted-foreground text-xs sm:text-sm">
          Describe your website in detail. We'll use this to generate content and design suggestions.
        </p>
      </div>

      <div className="space-y-4">
        <textarea
          value={formData.description}
          onChange={(e) => {
            updateFormData({ description: e.target.value });
            if (e.target.value.trim()) {
              setErrors(prev => ({ ...prev, description: '' }));
            }
          }}
          placeholder="Example: I need a modern e-commerce site for handmade ceramics with an earthy color scheme..."
          className={`w-full h-40 p-3 border rounded-md text-sm sm:text-base focus:ring-2 transition resize-none ${
            errors.description ? 'border-red-500 focus:ring-red-500' : 'border-input focus:ring-blue-500'
          }`}
          maxLength={3000}
        />
        {errors.description && (
          <p className="text-sm text-red-500">{errors.description}</p>
        )}

        {/* Desktop: show counter after button | Mobile: show before */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
          <span className="text-muted-foreground text-xs sm:hidden">
            {formData.description.length}/3000 characters
          </span>

          <Button 
            onClick={handleImproveBrief}
            disabled={!formData.description || isImproving}
            className="w-full sm:w-auto"
          >
            {isImproving ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Improving...
              </>
            ) : (
              'Improve with AI ✨'
            )}
          </Button>

          <span className="hidden sm:inline text-muted-foreground text-sm">
            {formData.description.length}/3000 characters
          </span>
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}
      </div>
    </div>
  );
};

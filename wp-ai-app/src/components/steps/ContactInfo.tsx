// //working, but not responsive
// 'use client';

// import React, { useState } from 'react';
// import { Input } from '@/components/ui/input';
// import { Textarea } from '@/components/ui/textarea';
// import { FaFacebook, FaInstagram, FaTwitter, FaYoutube, FaLinkedin } from 'react-icons/fa';
// import { FiPlus, FiX } from 'react-icons/fi';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';

// const socialIcons = {
//   facebook: <FaFacebook className="text-blue-600" />,
//   twitter: <FaTwitter className="text-blue-400" />,
//   instagram: <FaInstagram className="text-pink-600" />,
//   youtube: <FaYoutube className="text-red-600" />,
//   linkedin: <FaLinkedin className="text-blue-800" />,
// };

// const ContactInfo: React.FC = () => {
//   const { formData, updateFormData, errors, setErrors } = useSiteBuilder();
//   const [showSocialMediaMenu, setShowSocialMediaMenu] = useState(false);

//   const handleSocialMediaAdd = (platform: keyof typeof socialIcons) => {
//     if (!formData.contact.socialMedia[platform]) {
//       updateFormData({
//         contact: {
//           ...formData.contact,
//           socialMedia: {
//             ...formData.contact.socialMedia,
//             [platform]: '',
//           },
//         },
//       });
//     }
//     setShowSocialMediaMenu(false);
//   };

//   const handleSocialMediaChange = (platform: keyof typeof socialIcons, url: string) => {
//     updateFormData({
//       contact: {
//         ...formData.contact,
//         socialMedia: {
//           ...formData.contact.socialMedia,
//           [platform]: url,
//         },
//       },
//     });
//   };

//   const handleSocialMediaRemove = (platform: keyof typeof socialIcons) => {
//     const updatedSocial = { ...formData.contact.socialMedia };
//     delete updatedSocial[platform];

//     updateFormData({
//       contact: {
//         ...formData.contact,
//         socialMedia: updatedSocial,
//       },
//     });
//   };

//   return (
//     <div className="space-y-6">
//       <h2 className="text-xl font-semibold mb-2">How can people get in touch with you?</h2>

//       {/* Email & Phone */}
//       <div className="flex gap-4">
//         <div className="flex-1 space-y-2">
//           <label className="text-sm font-medium">Email *</label>
//           <Input
//             type="email"
//             placeholder="<EMAIL>"
//             value={formData.contact.email}
//             onChange={(e) =>
//               updateFormData({ contact: { ...formData.contact, email: e.target.value } })
//             }
//             className={errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}
//           />
//           {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
//         </div>

//         <div className="flex-1 space-y-2">
//           <label className="text-sm font-medium">Phone Number</label>
//           <Input
//             type="tel"
//             placeholder="Your phone number"
//             value={formData.contact.phone}
//             onChange={(e) =>
//               updateFormData({ contact: { ...formData.contact, phone: e.target.value } })
//             }
//           />
//         </div>
//       </div>

//       {/* Address */}
//       <div className="space-y-2">
//         <label className="text-sm font-medium">Address</label>
//         <Textarea
//           placeholder="123 Main Street, City, Country"
//           value={formData.contact.address}
//           onChange={(e) =>
//             updateFormData({ contact: { ...formData.contact, address: e.target.value } })
//           }
//           rows={3}
//         />
//       </div>

//       {/* Social Media */}
//       <div className="space-y-4">
//         <div className="flex items-center justify-between">
//           <span className="text-sm font-medium">Social Media</span>
//             <div className="relative">
//               <button
//                 type="button"
//                 onClick={() => setShowSocialMediaMenu(!showSocialMediaMenu)}
//                 className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100 hover:bg-blue-200 transition-transform duration-300"
//                 aria-label="Toggle Social Media Menu"
//               >
//                 <span
//                   className={`transition-transform duration-300 ease-in-out transform ${
//                     showSocialMediaMenu ? 'rotate-90 scale-110 text-red-500' : 'rotate-0 text-blue-600'
//                   }`}
//                 >
//                   {showSocialMediaMenu ? <FiX /> : <FiPlus />}
//                 </span>
//               </button>

//               <div
//                 className={`absolute right-0 mt-3 w-48 bg-white border rounded-lg shadow-lg p-2 z-10 transform transition-all duration-200 ease-in-out origin-top ${
//                   showSocialMediaMenu ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'
//                 }`}
//               >
//                 {(['facebook', 'twitter', 'instagram', 'youtube', 'linkedin'] as const).map((platform) => (
//                   <button
//                     key={platform}
//                     type="button"
//                     onClick={() => handleSocialMediaAdd(platform)}
//                     className="flex items-center w-full p-2 hover:bg-gray-100 rounded transition"
//                   >
//                     {socialIcons[platform]} <span className="ml-2 capitalize">{platform}</span>
//                   </button>
//                 ))}
//               </div>
//             </div>
//         </div>

//         <div className="space-y-3">
//           {Object.entries(formData.contact.socialMedia).map(([platform, url]) => (
//             <div key={platform} className="flex items-center gap-2">
//               {socialIcons[platform as keyof typeof socialIcons]}
//               <Input
//                 type="url"
//                 placeholder={`https://${platform}.com/yourprofile`}
//                 value={url}
//                 onChange={(e) =>
//                   handleSocialMediaChange(platform as keyof typeof socialIcons, e.target.value)
//                 }
//                 className="flex-1"
//               />
//               <button
//                 type="button"
//                 onClick={() => handleSocialMediaRemove(platform as keyof typeof socialIcons)}
//                 className="p-1 rounded hover:bg-red-100 text-red-600"
//                 aria-label={`Remove ${platform}`}
//               >
//                 ✕
//               </button>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export { ContactInfo };




'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FaFacebook, FaInstagram, FaTwitter, FaYoutube, FaLinkedin } from 'react-icons/fa';
import { FiPlus, FiX } from 'react-icons/fi';
import { useSiteBuilder } from '@/context/SiteBuilderContext';

const socialIcons = {
  facebook: <FaFacebook className="text-blue-600" />,
  twitter: <FaTwitter className="text-blue-400" />,
  instagram: <FaInstagram className="text-pink-600" />,
  youtube: <FaYoutube className="text-red-600" />,
  linkedin: <FaLinkedin className="text-blue-800" />,
};

const ContactInfo: React.FC = () => {
  const { formData, updateFormData, errors, setErrors } = useSiteBuilder();
  const [showSocialMediaMenu, setShowSocialMediaMenu] = useState(false);

  const handleSocialMediaAdd = (platform: keyof typeof socialIcons) => {
    if (!formData.contact.socialMedia[platform]) {
      updateFormData({
        contact: {
          ...formData.contact,
          socialMedia: {
            ...formData.contact.socialMedia,
            [platform]: '',
          },
        },
      });
    }
    setShowSocialMediaMenu(false);
  };

  const handleSocialMediaChange = (platform: keyof typeof socialIcons, url: string) => {
    updateFormData({
      contact: {
        ...formData.contact,
        socialMedia: {
          ...formData.contact.socialMedia,
          [platform]: url,
        },
      },
    });
  };

  const handleSocialMediaRemove = (platform: keyof typeof socialIcons) => {
    const updatedSocial = { ...formData.contact.socialMedia };
    delete updatedSocial[platform];

    updateFormData({
      contact: {
        ...formData.contact,
        socialMedia: updatedSocial,
      },
    });
  };

  return (
    <div className="space-y-6 px-4 sm:px-6">
      <h2 className="text-xl font-semibold mb-2">How can people get in touch with you?</h2>

      {/* Email & Phone */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 space-y-2">
          <label className="text-sm font-medium">Email</label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={formData.contact.email}
            onChange={(e) =>
              updateFormData({ contact: { ...formData.contact, email: e.target.value } })
            }
            className={`text-sm ${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>

        <div className="flex-1 space-y-2">
          <label className="text-sm font-medium">Phone Number</label>
          <Input
            type="tel"
            placeholder="Your phone number"
            value={formData.contact.phone}
            onChange={(e) =>
              updateFormData({ contact: { ...formData.contact, phone: e.target.value } })
            }
            className="text-sm"
          />
        </div>
      </div>

      {/* Address */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Address</label>
        <Textarea
          placeholder="123 Main Street, City, Country"
          value={formData.contact.address}
          onChange={(e) =>
            updateFormData({ contact: { ...formData.contact, address: e.target.value } })
          }
          rows={3}
          className="text-sm"
        />
      </div>

      {/* Social Media */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Social Media</span>
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowSocialMediaMenu(!showSocialMediaMenu)}
              className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100 hover:bg-blue-200 transition-transform duration-300"
              aria-label="Toggle Social Media Menu"
            >
              <span
                className={`transition-transform duration-300 ease-in-out transform ${
                  showSocialMediaMenu ? 'rotate-90 scale-110 text-red-500' : 'rotate-0 text-blue-600'
                }`}
              >
                {showSocialMediaMenu ? <FiX /> : <FiPlus />}
              </span>
            </button>

            <div
              className={`fixed left-4 right-4 bottom-24 sm:absolute sm:left-0 sm:right-auto sm:bottom-auto mt-2 w-[calc(100%-2rem)] sm:w-48 bg-white border rounded-lg shadow-lg p-2 z-[9999] transition-all duration-200 ease-in-out origin-top ${
                showSocialMediaMenu
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-95 pointer-events-none'
              }`}
            >
              {(['facebook', 'twitter', 'instagram', 'youtube', 'linkedin'] as const).map(
                (platform) => (
                  <button
                    key={platform}
                    type="button"
                    onClick={() => handleSocialMediaAdd(platform)}
                    className="flex items-center w-full p-2 hover:bg-gray-100 rounded transition"
                  >
                    {socialIcons[platform]} <span className="ml-2 capitalize">{platform}</span>
                  </button>
                )
              )}
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {Object.entries(formData.contact.socialMedia).map(([platform, url]) => (
            <div key={platform} className="flex flex-col sm:flex-row sm:items-center gap-2">
              {socialIcons[platform as keyof typeof socialIcons]}
              <Input
                type="url"
                placeholder={`https://${platform}.com/yourprofile`}
                value={url}
                onChange={(e) =>
                  handleSocialMediaChange(platform as keyof typeof socialIcons, e.target.value)
                }
                className="flex-1 text-sm"
              />
              <button
                type="button"
                onClick={() => handleSocialMediaRemove(platform as keyof typeof socialIcons)}
                className="p-1 rounded hover:bg-red-100 text-red-600"
                aria-label={`Remove ${platform}`}
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export { ContactInfo };

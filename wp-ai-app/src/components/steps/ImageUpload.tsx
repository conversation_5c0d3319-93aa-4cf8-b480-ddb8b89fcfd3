'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useSiteBuilder } from '@/context/SiteBuilderContext';
import { Button } from '@/components/ui/button';
import { FiUpload, FiX, FiCheck } from 'react-icons/fi';

const TABS = ['Pexels Images', 'Upload Images', 'Selected Images'];

interface PexelsImage {
  url: string;
  author: string;
}

interface OriginalImageData {
  url: string;
  width: number;
  height: number;
  aspect_ratio: number;
}

const ImageUpload = () => {
  const { formData, updateFormData, setGlobalLoading } = useSiteBuilder();

  const API_BASE = process.env.NEXT_PUBLIC_WORDPRESS_API!;

  const [selectedTab, setSelectedTab] = useState('Pexels Images');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const selectedImages = formData.selectedImages || [];
  const uploadedImages = formData.uploadedImages || [];
  const [pexelsImages, setPexelsImages] = useState<PexelsImage[]>([]);
  const [isLoadingPexels, setIsLoadingPexels] = useState(false);
  const [pexelsPage, setPexelsPage] = useState(1);
  const [hasMorePexels, setHasMorePexels] = useState(true);
  const [selectedOrientation, setSelectedOrientation] = useState<string>('all');
  const uploadMoreRef = useRef<HTMLInputElement | null>(null);

  const [loadedImageMap, setLoadedImageMap] = useState<Record<string, boolean>>({});

  const [originalImagesData, setOriginalImagesData] = useState<OriginalImageData[]>([]);

  const category = formData.category;
  const PEXELS_API_KEY = process.env.NEXT_PUBLIC_PEXELS_API_KEY;



  const getCacheKey = (query: string, orientation: string) =>
    `pexels_cache_${query}_${orientation}`;

  const saveToCache = (
    key: string,
    images: PexelsImage[],
  ) => {
    const data = {
      images,
      timestamp: Date.now(),
    };
    localStorage.setItem(key, JSON.stringify(data));
  };

  const loadFromCache = (key: string): PexelsImage[] | null => {
    const data = localStorage.getItem(key);
    if (!data) return null;
    try {
      const parsed = JSON.parse(data);
      if (Date.now() - parsed.timestamp > 3600000) return null; // 1 hour = 3600000ms
      return parsed.images;
    } catch {
      return null;
    }
  };

  const loadPexelsImages = async (page = 1, keyword?: string) => {
    const query = keyword ?? category;
    if (!query) return;

    const cacheKey = getCacheKey(query, selectedOrientation);

    if (page === 1) {
      const cachedImages = loadFromCache(cacheKey);
      if (cachedImages) {
        setPexelsImages(cachedImages);
        const newMap: Record<string, boolean> = {};
        for (const img of cachedImages) newMap[img.url] = false;
        setLoadedImageMap(newMap);
        setGlobalLoading(true);
        return;
      }
    }

    setIsLoadingPexels(true);
    setGlobalLoading(true);

    try {
      const orientationParam = selectedOrientation !== 'all' ? `&orientation=${selectedOrientation}` : '';
      const response = await fetch(
        `https://api.pexels.com/v1/search?query=${encodeURIComponent(
          query
        )}&per_page=15&page=${page}${orientationParam}`,
        {
          headers: { Authorization: PEXELS_API_KEY || '' },
        }
      );
      const data = await response.json();

      const newImages: PexelsImage[] = data.photos.map((p: any) => ({
        url: p.src.original,
        author: p.photographer,
      }));

      setHasMorePexels(newImages.length > 0);
      setPexelsPage(page);

      if (page === 1) {
        setPexelsImages(newImages);
        saveToCache(cacheKey, newImages);
        const newMap: Record<string, boolean> = {};
        for (const img of newImages) newMap[img.url] = false;
        setLoadedImageMap(newMap);
      } else {
        setPexelsImages((prev) => {
          const updated = [...prev, ...newImages];
          saveToCache(cacheKey, updated); // ✅ merge & cache updated list
          return updated;
        });
      }
    } catch (err) {
      console.error('Error loading Pexels:', err);
    } finally {
      setIsLoadingPexels(false);
    }
  };

  
  // Hide loader when all images are marked loaded
  useEffect(() => {
    if (pexelsImages.length === 0) return;

    let allLoaded = true;
    for (let img of pexelsImages) {
      if (!loadedImageMap[img.url]) {
        allLoaded = false;
        break;
      }
    }

    if (allLoaded) {
      setGlobalLoading(false);
    }
  }, [pexelsImages, loadedImageMap]);

  useEffect(() => {
    loadPexelsImages(1, searchTerm || category);
  }, [category, selectedOrientation, searchTerm]);

  useEffect(() => {
    const fetchOriginalImageData = async () => {
      try {
        const response = await fetch(`${API_BASE}/wp-json/custom/v1/page-image-map`);
        const data = await response.json();
        if (data && data.length > 0 && data[0].images) {
            setOriginalImagesData(data[0].images);
        }
      } catch (error) {
        console.error('Error fetching original image data:', error);
      }
    };

    fetchOriginalImageData();
  }, []);  

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchTerm(searchInput.trim());
  };

  // Modify the handleUploadImages function to match dimensions
  const handleUploadImages = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    
    const uploaded: string[] = [];
    const dimensionInfo: {url: string, width: number, height: number}[] = [];

    for (let file of Array.from(files)) {
      // Get the original image that matches by name
      const fileName = file.name.split('.')[0].toLowerCase();
      const originalImage = originalImagesData.find(img => 
        img.url.toLowerCase().includes(fileName)
      );

      // If we found a matching original image, create a resized version
      if (originalImage) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('target_width', originalImage.width.toString());
        formData.append('target_height', originalImage.height.toString());
        
        const res = await fetch('/api/upload-to-wp', {
          method: 'POST',
          body: formData,
        });
        const result = await res.json();
        uploaded.push(result.url);
        dimensionInfo.push({
          url: result.url,
          width: originalImage.width,
          height: originalImage.height
        });
      } else {
        // Fallback to regular upload if no match found
        const formData = new FormData();
        formData.append('file', file);
        const res = await fetch('/api/upload-to-wp', {
          method: 'POST',
          body: formData,
        });
        const result = await res.json();
        uploaded.push(result.url);
      }
    }

    updateFormData({
      uploadedImages: [...uploadedImages, ...uploaded],
      selectedImages: [...selectedImages, ...uploaded],
      imageDimensions: {
        ...formData.imageDimensions,
        ...dimensionInfo.reduce((acc, curr) => {
          acc[curr.url] = { width: curr.width, height: curr.height };
          return acc;
        }, {} as Record<string, {width: number, height: number}>)
      }
    });

    if (uploadMoreRef.current) {
      uploadMoreRef.current.value = '';
    }
  };

  const handleImageClick = (img: string) => {
    const isSelected = selectedImages.includes(img);
    updateFormData({
      selectedImages: isSelected
        ? selectedImages.filter((i) => i !== img)
        : [...selectedImages, img],
    });
  };

  const handleRemove = (img: string) => {
    updateFormData({
      uploadedImages: uploadedImages.filter((i) => i !== img),
      selectedImages: selectedImages.filter((i) => i !== img),
    });
  };

  return (
    <div className="flex flex-col h-screen w-full bg-gray-50">
      {/* Tabs */}
      <div className="w-full border-b bg-white shadow-sm">
        <div className="flex justify-center space-x-10 py-4">
          {TABS.map((tab) => {
            const count =
              tab === 'Selected Images'
                ? selectedImages.length
                : tab === 'Upload Images'
                ? uploadedImages.length
                : 0;

            return (
              <button
                key={tab}
                className={`text-md font-light tracking-wide relative transition duration-300 ${
                  selectedTab === tab ? 'text-blue-600' : 'text-gray-500'
                }`}
                onClick={() => setSelectedTab(tab)}
              >
                {count > 0 ? `${tab} (${count})` : tab}
                {selectedTab === tab && (
                  <div className="absolute bottom-[-6px] left-0 right-0 h-[2px] bg-blue-600 mx-auto w-full transition-all" />
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6 relative">
        {/* PEXELS TAB */}
        {selectedTab === 'Pexels Images' && (
          <>
            <form onSubmit={handleSearch} className="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <input
                type="text"
                placeholder="Search by keywords"
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
              />
              <Button type="submit" className="w-full sm:w-auto">Search</Button>
              <select
                className="w-full sm:w-[160px] border border-gray-300 rounded px-3 py-[10px] text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-300"
                value={selectedOrientation}
                onChange={(e) => setSelectedOrientation(e.target.value)}
              >
                <option value="all">All Orientation</option>
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </form>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
              {pexelsImages.map((img, idx) => {
                const isSelected = selectedImages.includes(img.url);
                const isImgLoaded = loadedImageMap[img.url];

                const handleImgLoad = (url: string) => {
                  setLoadedImageMap((prev) => ({
                    ...prev,
                    [img.url]: true,
                  }));
                };

                  return (
                    <div key={idx} className="relative flex flex-col">
                      <img
                        src={img.url}
                        onLoad={() => handleImgLoad(img.url)}
                        className={`rounded cursor-pointer border-4 transition duration-200 w-full h-auto aspect-[4/3] object-cover ${
                          isSelected ? 'border-blue-500' : 'border-transparent'
                        } ${!isImgLoaded ? 'opacity-0' : 'opacity-100'}`}
                        onClick={() => handleImageClick(img.url)}
                      />
                      {isImgLoaded && (
                        <>
                          {isSelected && (
                            <div className="absolute top-1 right-1 bg-green-500 text-white rounded-full p-1">
                              <FiCheck size={16} />
                            </div>
                          )}
                          <p className="text-[11px] text-gray-500 mt-1 text-left">
                            by {img.author} via Pexels
                          </p>
                        </>
                      )}
                    </div>
                  );
              })}
            </div>

            {hasMorePexels && (
              <div className="flex justify-center mt-6">
                <Button onClick={() => loadPexelsImages(pexelsPage + 1, searchTerm || category)}>
                  {isLoadingPexels ? 'Loading...' : 'Load More'}
                </Button>
              </div>
            )}
          </>
        )}

        {/* UPLOAD TAB */}
        {selectedTab === 'Upload Images' && (
          <div className="flex flex-col gap-6 h-full">
            {uploadedImages.length === 0 ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="relative w-full max-w-md">
                  <input
                    type="file"
                    id="upload"
                    multiple
                    className="absolute inset-0 opacity-0 z-10 cursor-pointer"
                    onChange={handleUploadImages}
                    ref={uploadMoreRef}
                  />
                  <div className="z-0 text-center border-2 border-dashed border-gray-300 rounded-lg p-6 bg-white">
                    <FiUpload className="text-4xl text-gray-400 mb-2 mx-auto" />
                    <p className="text-sm text-gray-500">Click or drag files to upload</p>
                    <p className="text-xs text-gray-400 mt-1">Supported formats: JPG, PNG, WEBP</p>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <div className="flex justify-end">
                  <input
                    type="file"
                    id="upload-more"
                    multiple
                    className="hidden"
                    ref={uploadMoreRef}
                    onChange={handleUploadImages}
                  />
                  <Button
                    variant="outline"
                    onClick={() => uploadMoreRef.current?.click()}
                  >
                    <FiUpload className="mr-2" /> Upload More
                  </Button>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {uploadedImages.map((img, idx) => {
                    const isSelected = selectedImages.includes(img);
                    return (
                      <div
                        key={idx}
                        className={`relative group border-4 rounded overflow-hidden cursor-pointer transition ${
                          isSelected ? 'border-blue-500' : 'border-transparent'
                        }`}
                        onClick={() => handleImageClick(img)}
                      >
                        <img src={img} className="w-full object-cover" />
                        {isSelected && (
                          <div className="absolute top-1 right-1 bg-green-500 text-white rounded-full p-1">
                            <FiCheck size={16} />
                          </div>
                        )}
                        <button
                          className="absolute top-1 left-1 bg-red-600 text-white rounded-full p-1 text-xs opacity-0 group-hover:opacity-100 transition"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemove(img);
                          }}
                        >
                          <FiX />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </>
            )}
          </div>
        )}

        {/* SELECTED TAB */}
        {selectedTab === 'Selected Images' && (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {selectedImages.length === 0 ? (
              <div className="col-span-full flex flex-col items-center justify-center h-full">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <p className="text-lg text-gray-600">You have not selected any images yet.</p>
                </div>
              </div>
            ) : (
              selectedImages.map((img, idx) => (
                <div key={idx} className="relative">
                  <img src={img} className="w-full object-cover rounded border-4 border-blue-400" />
                  <button
                    className="absolute top-1 right-1 bg-red-600 text-white rounded-full p-1 text-xs"
                    onClick={() => handleImageClick(img)}
                  >
                    <FiX />
                  </button>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageUpload;

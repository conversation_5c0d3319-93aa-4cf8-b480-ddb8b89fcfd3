"use client";

import React, { useState } from 'react';
import { Edit, Trash2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock, XCircle, AlertCircle } from 'lucide-react';

export interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

interface DNSRecordsTableProps {
  records: DNSRecord[];
  domainName: string;
  onEdit: (record: DNSRecord) => void;
  onDelete: (record: DNSRecord) => void;
  loading?: boolean;
}

const DNSRecordsTable: React.FC<DNSRecordsTableProps> = ({
  records,
  domainName,
  onEdit,
  onDelete,
  loading = false
}) => {
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const getStatusIcon = (status: DNSRecord['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: DNSRecord['status']) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'pending':
        return 'Pending';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: DNSRecord['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeColor = (type: DNSRecord['type']) => {
    switch (type) {
      case 'A':
        return 'text-blue-600 bg-blue-50';
      case 'AAAA':
        return 'text-purple-600 bg-purple-50';
      case 'CNAME':
        return 'text-green-600 bg-green-50';
      case 'MX':
        return 'text-orange-600 bg-orange-50';
      case 'TXT':
        return 'text-indigo-600 bg-indigo-50';
      case 'NS':
        return 'text-red-600 bg-red-50';
      case 'PTR':
        return 'text-pink-600 bg-pink-50';
      case 'SRV':
        return 'text-teal-600 bg-teal-50';
      case 'CAA':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const handleCopy = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const formatTTL = (ttl: number) => {
    if (ttl < 60) return `${ttl}s`;
    if (ttl < 3600) return `${Math.floor(ttl / 60)}m`;
    if (ttl < 86400) return `${Math.floor(ttl / 3600)}h`;
    return `${Math.floor(ttl / 86400)}d`;
  };

  const truncateValue = (value: string, maxLength: number = 50) => {
    if (value.length <= maxLength) return value;
    return `${value.substring(0, maxLength)}...`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-8">
          <Clock className="w-6 h-6 mr-3 text-gray-400 animate-spin" />
          <span className="text-gray-600">Loading DNS records...</span>
        </div>
      </div>
    );
  }

  if (records.length === 0) {
    return (
      <div className="p-6">
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <AlertCircle className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No DNS Records</h3>
          <p className="text-gray-500 mb-4">
            No DNS records have been configured for this domain yet.
          </p>
          <p className="text-sm text-gray-400">
            Add your first DNS record to get started.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            DNS Records ({records.length})
          </h3>
          <p className="text-sm text-gray-500">
            Domain: {domainName}
          </p>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Value
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                TTL
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {records.map((record) => (
              <tr key={record.id} className="hover:bg-gray-50">
                {/* Type */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(record.type)}`}>
                    {record.type}
                  </span>
                </td>

                {/* Name */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {record.name || '@'}
                    </span>
                    <button
                      onClick={() => handleCopy(record.name || '@', `name-${record.id}`)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                      title="Copy name"
                    >
                      {copiedId === `name-${record.id}` ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </td>

                {/* Value */}
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-900 max-w-xs truncate" title={record.value}>
                      {truncateValue(record.value)}
                    </span>
                    <button
                      onClick={() => handleCopy(record.value, `value-${record.id}`)}
                      className="text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
                      title="Copy value"
                    >
                      {copiedId === `value-${record.id}` ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </td>

                {/* TTL */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-900">
                    {formatTTL(record.ttl)}
                  </span>
                </td>

                {/* Priority */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-900">
                    {record.priority !== undefined ? record.priority : '-'}
                  </span>
                </td>

                {/* Status */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(record.status)}
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(record.status)}`}>
                      {getStatusText(record.status)}
                    </span>
                  </div>
                </td>

                {/* Actions */}
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onEdit(record)}
                      className="text-indigo-600 hover:text-indigo-900 transition-colors"
                      title="Edit record"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onDelete(record)}
                      className="text-red-600 hover:text-red-900 transition-colors"
                      title="Delete record"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Table Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>
            Showing {records.length} DNS record{records.length !== 1 ? 's' : ''}
          </span>
          <span>
            Last updated: {new Date().toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default DNSRecordsTable; 
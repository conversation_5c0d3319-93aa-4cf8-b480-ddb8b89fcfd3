"use client";

import React, { useState, useEffect } from 'react';
import { X, AlertCircle, CheckCircle } from 'lucide-react';

export interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

interface AddDNSRecordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (record: Omit<DNSRecord, 'id' | 'domain_id' | 'created_at' | 'updated_at'>) => void;
  domainId: string;
  domainName: string;
}

const DNS_RECORD_TYPES = [
  { value: 'A', label: 'A Record', description: 'IPv4 address' },
  { value: 'AAAA', label: 'AAAA Record', description: 'IPv6 address' },
  { value: 'CNAME', label: 'CNAME Record', description: 'Canonical name' },
  { value: 'MX', label: 'MX Record', description: 'Mail exchange' },
  { value: 'TXT', label: 'TXT Record', description: 'Text record' },
  { value: 'NS', label: 'NS Record', description: 'Name server' },
  { value: 'PTR', label: 'PTR Record', description: 'Pointer record' },
  { value: 'SRV', label: 'SRV Record', description: 'Service record' },
  { value: 'CAA', label: 'CAA Record', description: 'Certificate authority' },
] as const;

const TTL_OPTIONS = [
  { value: 60, label: '1 minute' },
  { value: 300, label: '5 minutes' },
  { value: 900, label: '15 minutes' },
  { value: 1800, label: '30 minutes' },
  { value: 3600, label: '1 hour' },
  { value: 7200, label: '2 hours' },
  { value: 14400, label: '4 hours' },
  { value: 28800, label: '8 hours' },
  { value: 43200, label: '12 hours' },
  { value: 86400, label: '1 day' },
  { value: 172800, label: '2 days' },
  { value: 604800, label: '1 week' },
];

const AddDNSRecordModal: React.FC<AddDNSRecordModalProps> = ({
  isOpen,
  onClose,
  onAdd,
  domainId,
  domainName
}) => {
  const [formData, setFormData] = useState({
    type: 'A' as DNSRecord['type'],
    name: '',
    value: '',
    ttl: 3600,
    priority: 10,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        type: 'A',
        name: '',
        value: '',
        ttl: 3600,
        priority: 10,
      });
      setErrors({});
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.includes(' ')) {
      newErrors.name = 'Name cannot contain spaces';
    }

    // Validate value
    if (!formData.value.trim()) {
      newErrors.value = 'Value is required';
    } else {
      // Type-specific validation
      switch (formData.type) {
        case 'A':
          if (!/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(formData.value)) {
            newErrors.value = 'Invalid IPv4 address format';
          }
          break;
        case 'AAAA':
          if (!/^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(formData.value)) {
            newErrors.value = 'Invalid IPv6 address format';
          }
          break;
        case 'CNAME':
          if (!/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/.test(formData.value)) {
            newErrors.value = 'Invalid domain name format';
          }
          break;
        case 'MX':
          if (!/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/.test(formData.value)) {
            newErrors.value = 'Invalid mail server domain format';
          }
          break;
        case 'TXT':
          if (formData.value.length > 255) {
            newErrors.value = 'TXT record value cannot exceed 255 characters';
          }
          break;
      }
    }

    // Validate priority for MX records
    if (formData.type === 'MX') {
      if (formData.priority < 0 || formData.priority > 65535) {
        newErrors.priority = 'Priority must be between 0 and 65535';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const recordData = {
        type: formData.type,
        name: formData.name.trim(),
        value: formData.value.trim(),
        ttl: formData.ttl,
        priority: formData.type === 'MX' ? formData.priority : undefined,
        status: 'pending' as const,
      };

      onAdd(recordData);
      onClose();
    } catch (error) {
      console.error('Error adding DNS record:', error);
      setErrors({ submit: 'Failed to add DNS record. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              Add DNS Record
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Domain info */}
          <div className="mb-6 p-3 bg-gray-50 rounded-md">
            <p className="text-sm text-gray-600">
              Domain: <span className="font-medium text-gray-900">{domainName}</span>
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Record Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Record Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value as DNSRecord['type'])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                {DNS_RECORD_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label} - {type.description}
                  </option>
                ))}
              </select>
            </div>

            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., www, mail, @ (for root domain)"
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.name}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Leave empty or use @ for the root domain
              </p>
            </div>

            {/* Value */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Value *
              </label>
              <input
                type="text"
                value={formData.value}
                onChange={(e) => handleInputChange('value', e.target.value)}
                placeholder={
                  formData.type === 'A' ? 'e.g., 192.168.1.1' :
                  formData.type === 'AAAA' ? 'e.g., 2001:db8::1' :
                  formData.type === 'CNAME' ? 'e.g., example.com' :
                  formData.type === 'MX' ? 'e.g., mail.example.com' :
                  formData.type === 'TXT' ? 'e.g., "v=spf1 include:_spf.google.com ~all"' :
                  'Enter value'
                }
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.value ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.value && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.value}
                </p>
              )}
            </div>

            {/* Priority (for MX records) */}
            {formData.type === 'MX' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority *
                </label>
                <input
                  type="number"
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
                  min="0"
                  max="65535"
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                    errors.priority ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.priority && (
                  <p className="mt-1 text-sm text-red-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.priority}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Lower numbers have higher priority
                </p>
              </div>
            )}

            {/* TTL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                TTL (Time To Live)
              </label>
              <select
                value={formData.ttl}
                onChange={(e) => handleInputChange('ttl', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                {TTL_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label} ({option.value}s)
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                How long DNS servers should cache this record
              </p>
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="p-3 border border-red-200 rounded-md bg-red-50">
                <p className="text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.submit}
                </p>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center px-4 py-2 text-white transition-colors bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Add Record
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddDNSRecordModal; 
"use client";

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, CheckCir<PERSON> } from 'lucide-react';

export interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

interface DeleteDNSRecordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  record: DNSRecord | null;
  domainName: string;
}

const DeleteDNSRecordModal: React.FC<DeleteDNSRecordModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  record,
  domainName
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState('');

  const handleConfirm = async () => {
    if (confirmText !== 'DELETE') {
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('Error deleting DNS record:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setConfirmText('');
    onClose();
  };

  if (!isOpen || !record) return null;

  const getRecordTypeDescription = (type: DNSRecord['type']) => {
    switch (type) {
      case 'A':
        return 'IPv4 address record';
      case 'AAAA':
        return 'IPv6 address record';
      case 'CNAME':
        return 'Canonical name record';
      case 'MX':
        return 'Mail exchange record';
      case 'TXT':
        return 'Text record';
      case 'NS':
        return 'Name server record';
      case 'PTR':
        return 'Pointer record';
      case 'SRV':
        return 'Service record';
      case 'CAA':
        return 'Certificate authority record';
      default:
        return 'DNS record';
    }
  };

  const getImpactDescription = (type: DNSRecord['type'], name: string) => {
    if (name === '@' || name === '') {
      return `This will remove the ${type} record for the root domain (${domainName}).`;
    }
    return `This will remove the ${type} record for ${name}.${domainName}.`;
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={handleClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-red-900">
              Delete DNS Record
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Warning Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </div>

          {/* Warning Message */}
          <div className="mb-6 text-center">
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              Are you sure you want to delete this DNS record?
            </h4>
            <p className="text-sm text-gray-600">
              This action cannot be undone and may affect your domain's functionality.
            </p>
          </div>

          {/* Record Details */}
          <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-700">Domain:</span>
                <span className="ml-2 text-sm text-gray-900">{domainName}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">Record Type:</span>
                <span className="ml-2 text-sm text-gray-900">
                  {record.type} ({getRecordTypeDescription(record.type)})
                </span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">Name:</span>
                <span className="ml-2 text-sm text-gray-900">
                  {record.name || '@'}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">Value:</span>
                <span className="ml-2 text-sm text-gray-900 break-all">
                  {record.value}
                </span>
              </div>
              {record.priority !== undefined && (
                <div>
                  <span className="text-sm font-medium text-gray-700">Priority:</span>
                  <span className="ml-2 text-sm text-gray-900">{record.priority}</span>
                </div>
              )}
              <div>
                <span className="text-sm font-medium text-gray-700">TTL:</span>
                <span className="ml-2 text-sm text-gray-900">{record.ttl}s</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-700">Status:</span>
                <span className={`ml-2 text-sm font-medium ${
                  record.status === 'active' ? 'text-green-600' :
                  record.status === 'pending' ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                </span>
              </div>
            </div>
          </div>

          {/* Impact Warning */}
          <div className="mb-6 p-4 border border-amber-200 rounded-md bg-amber-50">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 mr-2 text-amber-600 mt-0.5" />
              <div>
                <h5 className="text-sm font-medium text-amber-800 mb-1">
                  Potential Impact
                </h5>
                <p className="text-sm text-amber-700">
                  {getImpactDescription(record.type, record.name)}
                </p>
                <p className="text-xs text-amber-600 mt-2">
                  This may cause service interruptions if this record is actively being used.
                </p>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type "DELETE" to confirm
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder="DELETE"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={confirmText !== 'DELETE' || isDeleting}
              className="flex items-center px-4 py-2 text-white transition-colors bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Record
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteDNSRecordModal; 
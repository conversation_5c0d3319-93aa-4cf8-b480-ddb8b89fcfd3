'use client';
import React from 'react';
import { useSiteBuilder } from '../context/SiteBuilderContext';
import { StepIndicator } from './StepIndicator';
import { WebsiteInfo } from 'src/components/steps/WebsiteInfo';
import { WebsiteDescription } from 'src/components/steps/WebsiteDescription';
import { ContactInfo } from 'src/components/steps/ContactInfo';
import ImageUpload from 'src/components/steps/ImageUpload';
import { TemplateSelection } from 'src/components/steps/TemplateSelection';
import { Customization } from 'src/components/steps/Customization';
import { FinalGeneration } from 'src/components/steps/FinalGeneration';

export const SiteBuilder: React.FC = () => {
  const { formData } = useSiteBuilder();

  const renderStep = () => {
    switch (formData.step) {
      case 1:
        return <WebsiteInfo />;
      case 2:
        return <WebsiteDescription />;
      case 3:
        return <ContactInfo />;
      case 4:
        return <ImageUpload />;
      case 5:
        return <TemplateSelection />;
      case 6:
        return <Customization />;
      case 7:
        return <FinalGeneration />;
      default:
        return <WebsiteInfo />;
    }
  };


  return (
    <div className="transition-all duration-300 ease-in-out">
      {renderStep()}
    </div>
  );
};

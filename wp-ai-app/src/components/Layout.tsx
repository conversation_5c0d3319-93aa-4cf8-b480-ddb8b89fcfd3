//working fine. no need to change anything
'use client';

import React from 'react';
import { StepIndicator } from './StepIndicator';
import { useSiteBuilder } from '@/context/SiteBuilderContext';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
  currentStep?: number;
  showFooterButtons?: boolean;
  onExitToChoice?: () => void; // ✅ ADDED
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  currentStep,
  showFooterButtons = true,
  onExitToChoice, // ✅ ADDED
}) => {
  const { nextStep, prevStep, validateStep } = useSiteBuilder();

  const isFullWidth = currentStep === 4 || currentStep === 5;
  const showHeader = currentStep && currentStep <= 5;
  const showFooter = currentStep && currentStep <= 5;

  const handleNextClick = () => {
    if (validateStep()) {
      nextStep();
    }
  };

  const handleBackClick = () => {
    if (currentStep === 1) {
      onExitToChoice?.(); // ✅ SAFELY TRIGGER RETURN TO CHOICE
    } else {
      prevStep();
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
      {showHeader && (
        <header className="sticky top-0 z-50 bg-white shadow-md py-4">
          <div className="relative max-w-5xl mx-auto px-4 flex items-center justify-center">
            <StepIndicator currentStep={currentStep!} />
          </div>
        </header>
      )}

      <main
        className={`flex-grow w-full h-full overflow-hidden ${
          isFullWidth ? 'px-6 py-10' : 'max-w-4xl mx-auto px-6 py-6'
        }`}
      >
        {currentStep && currentStep <= 3 ? (
          <div className="bg-white rounded-lg shadow p-6 space-y-6">{children}</div>
        ) : (
          children
        )}
      </main>

      {showFooter && showFooterButtons && (
        <footer className="sticky bottom-0 z-50 bg-white shadow-inner py-4 mt-auto">
          <div className="max-w-4xl mx-auto px-6 flex justify-between items-center">
            <button
              onClick={handleBackClick}
              className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button
              onClick={handleNextClick}
              className="w-10 h-10 flex items-center justify-center rounded-full bg-lime-500 text-white hover:bg-lime-600 transition"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </footer>
      )}
    </div>
  );
};


//testing the responsive design



// 'use client';

// import React from 'react';
// import { StepIndicator } from './StepIndicator';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';
// import { ChevronLeft, ChevronRight } from 'lucide-react';

// interface LayoutProps {
//   children: React.ReactNode;
//   currentStep?: number;
//   showFooterButtons?: boolean;
//   onExitToChoice?: () => void; // ✅ ADDED
// }

// export const Layout: React.FC<LayoutProps> = ({
//   children,
//   currentStep,
//   showFooterButtons = true,
//   onExitToChoice, // ✅ ADDED
// }) => {
//   const { nextStep, prevStep, validateStep } = useSiteBuilder();

//   const isFullWidth = currentStep === 4 || currentStep === 5;
//   const showHeader = currentStep && currentStep <= 5;
//   const showFooter = currentStep && currentStep <= 5;

//   const handleNextClick = () => {
//     if (validateStep()) {
//       nextStep();
//     }
//   };

//   const handleBackClick = () => {
//     if (currentStep === 1) {
//       onExitToChoice?.(); // ✅ SAFELY TRIGGER RETURN TO CHOICE
//     } else {
//       prevStep();
//     }
//   };

//   return (
//     <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
//       {showHeader && (
//         <header className="sticky top-0 z-50 bg-white shadow-md py-4">
//           <div className="relative max-w-5xl mx-auto px-4 flex items-center justify-center">
//             <StepIndicator currentStep={currentStep!} />
//           </div>
//         </header>
//       )}

//       <main
//         className={`flex-grow w-full px-4 py-6 ${
//           isFullWidth ? '' : 'max-w-md mx-auto'
//         }`}
//       >
//         {currentStep && currentStep <= 3 ? (
//           <div
//             className="bg-white rounded-xl shadow p-5 space-y-6 max-h-[calc(100vh-160px)] overflow-y-auto"
//             style={{ scrollbarGutter: 'stable' }}
//           >
//             {children}
//           </div>
//         ) : (
//           children
//         )}
//       </main>

//       {showFooter && showFooterButtons && (
//         <footer className="sticky bottom-0 z-50 bg-white shadow-inner py-4 mt-auto">
//           <div className="max-w-4xl mx-auto px-6 flex justify-between items-center">
//             <button
//               onClick={handleBackClick}
//               className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition"
//             >
//               <ChevronLeft className="w-5 h-5" />
//             </button>
//             <button
//               onClick={handleNextClick}
//               className="w-10 h-10 flex items-center justify-center rounded-full bg-lime-500 text-white hover:bg-lime-600 transition"
//             >
//               <ChevronRight className="w-5 h-5" />
//             </button>
//           </div>
//         </footer>
//       )}
//     </div>
//   );
// };

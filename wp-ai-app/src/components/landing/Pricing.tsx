import React, { useEffect, useRef } from 'react';
import { Check, <PERSON>R<PERSON>, <PERSON>rkles, Crown, Zap } from 'lucide-react';

const plans = [
  {
    name: 'Starter',
    price: '$29',
    period: '/month',
    description: 'Perfect for personal projects and small businesses',
    icon: Zap,
    color: 'from-[#7DBE1D] to-[#C5DB9D]',
    features: [
      '5 AI-generated websites',
      'Basic customization tools',
      'Mobile-responsive design',
      'SSL certificate included',
      'Basic SEO optimization',
      'Email support'
    ],
    popular: false
  },
  {
    name: 'Professional',
    price: '$79',
    period: '/month',
    description: 'Ideal for growing businesses and agencies',
    icon: Crown,
    color: 'from-[#7DBE1D] to-[#C5DB9D]',
    features: [
      'Unlimited AI websites',
      'Advanced design tools',
      'E-commerce integration',
      'Custom domain support',
      'Advanced SEO features',
      'Priority support',
      'Team collaboration',
      'Analytics dashboard'
    ],
    popular: true
  },
  {
    name: 'Enterprise',
    price: '$199',
    period: '/month',
    description: 'For large teams and high-volume projects',
    icon: Sparkles,
    color: 'from-[#7DBE1D] to-[#C5DB9D]',
    features: [
      'Everything in Professional',
      'White-label solution',
      'API access',
      'Custom integrations',
      'Dedicated account manager',
      '24/7 phone support',
      'Custom training',
      'SLA guarantee'
    ],
    popular: false
  }
];

const Pricing: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('.pricing-card');
            cards.forEach((card, index) => {
              setTimeout(() => {
                card.classList.add('animate-fade-in-up');
              }, index * 200);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Simple,{' '}
            <span className="bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] bg-clip-text text-transparent">
              Transparent Pricing
            </span>
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto mb-8">
            Choose the perfect plan for your needs. All plans include our core AI features and can be upgraded anytime.
          </p>

          <div className="inline-flex items-center bg-gradient-to-r from-[#E6F2D8] to-[#F4F8EC] rounded-full p-0.5 sm:p-1">
            <button className="text-sm sm:text-base bg-white text-[#7DBE1D] px-4 py-1.5 sm:px-6 sm:py-2 rounded-full font-medium shadow-sm">
              Monthly
            </button>
            <button className="text-sm sm:text-base text-[#7DBE1D] px-4 py-1.5 sm:px-6 sm:py-2 rounded-full font-medium hover:text-[#5BA009] transition">
                 Annual (Save 25%)
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`pricing-card opacity-0 transform translate-y-8 relative bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-sm hover:shadow-xl transition-all duration-500 border ${
                plan.popular
                  ? 'border-[#C5DB9D] ring-2 ring-[#7DBE1D] ring-opacity-50 scale-105'
                  : 'border-gray-200 hover:border-[#C5DB9D]'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                    Most Popular
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${plan.color} mb-4`}>
                  <plan.icon className="w-8 h-8 text-white" />
                </div>

                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-4">{plan.description}</p>

                <div className="flex items-baseline justify-center">
                  <span className="text-4xl sm:text-5xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-lg sm:text-xl text-gray-600 ml-1">{plan.period}</span>
                </div>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <Check className="w-5 h-5 text-[#7DBE1D] mr-3 flex-shrink-0" />
                    <span className="text-sm sm:text-base text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                className={`w-full py-3 sm:py-4 px-4 sm:px-6 rounded-xl text-base sm:text-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 group ${
                  plan.popular
                    ? 'bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] text-white hover:shadow-lg transform hover:scale-105'
                    : 'bg-gray-100 text-gray-900 hover:bg-[#E6F2D8]'
                }`}
              >
                <span>Get Started</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <p className="text-gray-600 mb-4">
            Need a custom solution? We've got you covered.
          </p>
          <button className="text-[#7DBE1D] hover:text-[#5BA009] font-semibold transition-colors duration-300">
            Contact Sales →
          </button>
        </div>
      </div>
    </section>
  );
};

export default Pricing;

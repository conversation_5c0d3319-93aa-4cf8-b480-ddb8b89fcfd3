// //without responsive design
// 'use client';

// import React, { useEffect, useState } from 'react';
// import Image from 'next/image';
// import Link from 'next/link';

// const Header: React.FC = () => {
//   const [show, setShow] = useState(true);
//   const [lastScrollY, setLastScrollY] = useState(0);

//   useEffect(() => {
//     const handleScroll = () => {
//       const currentScrollY = window.scrollY;
//       setShow(currentScrollY < lastScrollY || currentScrollY < 100);
//       setLastScrollY(currentScrollY);
//     };

//     window.addEventListener('scroll', handleScroll);
//     return () => window.removeEventListener('scroll', handleScroll);
//   }, [lastScrollY]);

//   return (
//     <header
//       className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-500 ${
//         show ? 'translate-y-0' : '-translate-y-full'
//       } bg-[#131336]/90 backdrop-blur-sm border-b border-[#6D6D81]/40`}
//     >
//       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="flex items-center justify-between h-16">
//           {/* Logo */}
//           <Link href="#home" className="flex items-center space-x-2">
//             <Image
//               src="/assets/template-thumbnails/logo.jpeg"
//               alt="Logo"
//               width={36}
//               height={36}
//               className="rounded-full"
//               priority
//             />
//             <span className="text-[#C5DB9D] font-bold text-lg">AI WordPress Builder</span>
//           </Link>

//           {/* Navigation */}
//           <nav className="flex space-x-6">
//             {[
//               { label: 'Home', href: '#home' },
//               { label: 'Features', href: '#features' },
//               { label: 'How It Works', href: '#how-it-works' },
//               { label: 'Pricing', href: '#pricing' },
//             ].map((item, i) => (
//               <a
//                 key={i}
//                 href={item.href}
//                 className="text-white hover:text-[#C5DB9D] transition duration-300 transform hover:scale-110 font-medium text-sm sm:text-base"
//               >
//                 {item.label}
//               </a>
//             ))}
//           </nav>
//         </div>
//       </div>
//     </header>
//   );
// };

// export default Header;


//with responsive design
'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FiMenu, FiX } from 'react-icons/fi';

const Header: React.FC = () => {
  const [show, setShow] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setShow(currentScrollY < lastScrollY || currentScrollY < 100);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const navItems = [
    { label: 'Home', href: '#home' },
    { label: 'Features', href: '#features' },
    { label: 'How It Works', href: '#how-it-works' },
    { label: 'Pricing', href: '#pricing' },
  ];

  const toggleMenu = () => setMobileMenuOpen((prev) => !prev);
  const closeMenu = () => setMobileMenuOpen(false);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-500 ${
        show ? 'translate-y-0' : '-translate-y-full'
      } bg-[#131336]/90 backdrop-blur-sm border-b border-[#6D6D81]/40`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="#home" className="flex items-center space-x-2">
            <Image
              src="/assets/template-thumbnails/logo.jpeg"
              alt="Logo"
              width={36}
              height={36}
              className="rounded-full"
              priority
            />
            <span className="text-[#C5DB9D] font-bold text-lg whitespace-nowrap">
              AI WordPress Builder
            </span>
          </Link>

          {/* Desktop Nav */}
          <nav className="hidden md:flex space-x-6">
            {navItems.map((item, i) => (
              <a
                key={i}
                href={item.href}
                className="text-white hover:text-[#C5DB9D] transition duration-300 transform hover:scale-110 font-medium text-sm sm:text-base"
              >
                {item.label}
              </a>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button onClick={toggleMenu} className="text-white focus:outline-none">
              {mobileMenuOpen ? <FiX size={28} /> : <FiMenu size={28} />}
            </button>
          </div>
        </div>
      </div>

    {/* Mobile Menu Overlay */}
    <div
      className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out bg-[#131336]/95 backdrop-blur-sm z-40 fixed top-16 left-0 right-0 ${
        mobileMenuOpen ? 'max-h-screen py-8' : 'max-h-0'
      }`}
    >
      <div className="flex flex-col items-center space-y-6 text-white text-lg font-medium">
        {navItems.map((item, i) => (
          <a
            key={i}
            href={item.href}
            onClick={closeMenu}
            className="hover:text-[#C5DB9D] transition duration-300"
          >
            {item.label}
          </a>
        ))}
      </div>
    </div>
    </header>
  );
};

export default Header;

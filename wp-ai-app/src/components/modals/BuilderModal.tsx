//working fine.
//wth loader
'use client';

import { useRouter } from 'next/navigation';
import { X } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSiteBuilder } from '@/context/SiteBuilderContext';
import GlobalLoader from '@/components/GlobalLoader';
import BuilderChoice from '@/components/builder/BuilderChoice';

// Wrapper component to pass props into dynamically imported BuilderPage
const BuilderPageWithProps = ({ onExitToChoice }: { onExitToChoice: () => void }) => {
  const BuilderPage = dynamic(() => import('@/app/builder/page'), { ssr: false });
  return <BuilderPage onExitToChoice={onExitToChoice} />;
};

export default function BuilderModal() {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'choice' | 'classic'>('choice');
  const [showLoader, setShowLoader] = useState(false);
  const backdropRef = useRef(null);

  const { setGlobalLoading } = useSiteBuilder();

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => router.push('/'), 300);
  };

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === backdropRef.current) {
      handleClose();
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          ref={backdropRef}
          onClick={handleBackdropClick}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        >
          <div className="relative w-full h-full sm:max-w-[82vw] sm:h-[82vh]">
            {/* Loader overlay */}
            {showLoader && (
              <div className="absolute inset-0 z-[9999] bg-white/70 backdrop-blur-sm flex items-center justify-center">
                <GlobalLoader />
              </div>
            )}

            <motion.div
              initial={{ scale: 0.95, opacity: 0, y: 40 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.95, opacity: 0, y: 20 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="w-full h-full sm:rounded-2xl bg-white dark:bg-[#f9fafb] shadow-2xl overflow-hidden p-4 sm:p-6"
            >
              <button
                onClick={handleClose}
                className="absolute flex items-center justify-center w-10 h-10 transition bg-white rounded-full shadow-md top-2 right-2 sm:-top-4 sm:-right-4 hover:bg-gray-100"
                aria-label="Close modal"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>

              {selectedMode === 'choice' && (
                <BuilderChoice
                  onSelectClassic={async () => {
                    setShowLoader(true);
                    setGlobalLoading(true);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    setGlobalLoading(false);
                    setShowLoader(false);
                    setSelectedMode('classic');
                  }}
                />
              )}

              {selectedMode === 'classic' && (
                <div className="flex flex-col w-full h-full">
                  <div className="flex-grow min-h-0">
                    <BuilderPageWithProps onExitToChoice={() => setSelectedMode('choice')} />
                  </div>
                </div>
              )}

            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

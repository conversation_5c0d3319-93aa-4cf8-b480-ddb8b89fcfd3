"use client";
import { useEffect, useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { EyeOpenIcon, EyeClosedIcon } from "@/components/ui/EyeIcons";

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (session) {
        window.location.href = "/dashboard";
      }
    });
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  const handleOAuthLogin = async (provider: "google" | "apple" | "azure") => {
    await supabase.auth.signInWithOAuth({ provider });
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    const { error, data } = await supabase.auth.signInWithPassword({ email, password });
    if (error) setError(error.message);
    else window.location.href = "/dashboard";
  };

  return (
    <div style={{
      minHeight: "100vh",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      background: "linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%)",
      padding: 16,
    }}>
      <div style={{
        background: "white",
        padding: "48px 32px",
        borderRadius: "16px",
        boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.10)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        maxWidth: 500,
        minWidth: 0,
        border: "1.5px solid #1DBF73",
      }}>
        <h2 style={{ marginBottom: 8, fontWeight: 700, fontSize: 26, color: "#1a3c34" }}>Welcome to WP AI</h2>
        <div style={{ width: "100%", margin: "24px 0 12px 0", display: "flex", gap: 8 }}>
          <button onClick={() => handleOAuthLogin("google")}
            style={{ flex: 1, background: "#fff", border: "1px solid #e0e0e0", borderRadius: 8, padding: 8, display: "flex", alignItems: "center", justifyContent: "center", gap: 8, fontWeight: 600, cursor: "pointer" }}>
          <svg width="18" height="18" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>            <span>Google</span>
          </button>
          <button onClick={() => handleOAuthLogin("azure")}
            style={{ flex: 1, background: "#fff", border: "1px solid #e0e0e0", borderRadius: 8, padding: 8, display: "flex", alignItems: "center", justifyContent: "center", gap: 8, fontWeight: 600, cursor: "pointer" }}>
          <svg width="18" height="18" viewBox="0 0 24 24">
            <path fill="#F25022" d="M11.4 11.4H2.6V2.6h8.8v8.8z"/>
            <path fill="#00A4EF" d="M21.4 11.4h-8.8V2.6h8.8v8.8z"/>
            <path fill="#7FBA00" d="M11.4 21.4H2.6v-8.8h8.8v8.8z"/>
            <path fill="#FFB900" d="M21.4 21.4h-8.8v-8.8h8.8v8.8z"/>
          </svg>            <span>Microsoft</span>
          </button>
        </div>
        <div style={{ width: "100%", display: "flex", alignItems: "center", margin: "16px 0 8px 0" }}>
          <div style={{ flex: 1, height: 1, background: "#e0e0e0" }} />
          <span style={{ margin: "0 12px", color: "#aaa", fontSize: 14 }}>OR</span>
          <div style={{ flex: 1, height: 1, background: "#e0e0e0" }} />
        </div>
        <form style={{ width: "100%", marginTop: 8 }} onSubmit={handleLogin}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ fontWeight: 500, color: "#222", fontSize: 15 }}>Email</label>
            <input
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
              style={{ width: "100%", padding: "10px 12px", border: "1px solid #e0e0e0", borderRadius: 8, marginTop: 6, fontSize: 15 }}
              placeholder="Enter your email"
            />
          </div>
          <div style={{ marginBottom: 8, position: "relative" }}>
            <label style={{ fontWeight: 500, color: "#222", fontSize: 15 }}>Password</label>
            <input
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              style={{ width: "100%", padding: "10px 12px", border: "1px solid #e0e0e0", borderRadius: 8, marginTop: 6, fontSize: 15 }}
              placeholder="Enter your password"
            />
            <span onClick={() => setShowPassword(v => !v)} style={{ position: "absolute", right: 12, top: 38, cursor: "pointer", color: "#888" }}>
              {showPassword ? <EyeClosedIcon /> : <EyeOpenIcon />}
            </span>
          </div>
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 16 }}>
            <label style={{ display: "flex", alignItems: "center", fontSize: 14, color: "#555" }}>
              <input type="checkbox" style={{ marginRight: 6 }} /> Remember me
            </label>
            <a href="#" style={{ color: "#1DBF73", fontWeight: 500, fontSize: 14, textDecoration: "none" }}>Forgot Password?</a>
          </div>
          {error && <div style={{ color: "#e53935", marginBottom: 8, fontSize: 14 }}>{error}</div>}
          <button type="submit" style={{ width: "100%", padding: "12px 0", background: "#1DBF73", color: "white", border: "none", borderRadius: 8, fontSize: 16, fontWeight: 600, marginBottom: 8, cursor: "pointer" }}>
            Sign In
          </button>
        </form>
        <div style={{ marginTop: 8, color: "#444", fontSize: 15 }}>
          Don&apos;t have an account? <a href="/signup" style={{ color: "#1DBF73", fontWeight: 600, textDecoration: "none" }}>Sign Up</a>
        </div>
      </div>
    </div>
  );
}
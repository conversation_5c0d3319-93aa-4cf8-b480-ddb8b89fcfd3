import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const { logoImageId } = await req.json();

  if (!logoImageId) {
    return NextResponse.json({ error: 'No logo image ID provided' }, { status: 400 });
  }

  const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API!;
  const username = process.env.WP_ADMIN_USER!;
  const appPassword = process.env.WP_APP_PASSWORD!;
  const credentials = Buffer.from(`${username}:${appPassword}`).toString('base64');

  try {
    const settingsRes = await fetch(`${BASE_URL}/wp-json/wp/v2/settings`, {
      headers: { Authorization: `Basic ${credentials}` },
    });

    const settings = await settingsRes.json();
    const frontPageId = settings.page_on_front;

    const fields: any = {
      site_logo: logoImageId
    };

    const updateRes = await fetch(`${BASE_URL}/wp-json/acf/v3/pages/${frontPageId}`, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${credentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fields }),
    });

    if (!updateRes.ok) {
      const errorText = await updateRes.text();
      console.error('ACF update error:', updateRes.status, errorText);
      return NextResponse.json({ error: `ACF update failed: ${errorText}` }, { status: updateRes.status });
    }

    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Network error:', error);
    return NextResponse.json({ error: 'Network error' }, { status: 500 });
  }
}

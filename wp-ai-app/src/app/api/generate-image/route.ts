import { OpenAI } from 'openai';
import { NextResponse } from 'next/server';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// 🔧 Utility to get image subject based on website category
const getCategorySpecificSubject = (category: string): string => {
  const subjects: Record<string, string> = {
    Business: 'Professional team collaboration or sleek office environment',
    'E-commerce': 'Premium product showcase with elegant arrangement and subtle branding',
    Restaurant: 'Mouthwatering signature dish or inviting dining atmosphere',
    Portfolio: 'Creative workspace with artistic elements and professional tools',
    Blog: 'Thoughtful workspace with notebook, coffee cup, and natural lighting',
    Nonprofit: 'Community gathering or meaningful outreach scene with diverse people',
    Personal: 'Comfortable lifestyle scene, cozy interior, or creative desk setup',
  };

  return subjects[category] || 'Professional representation of the brand';
};

// ✂️ Utility to sanitize and limit text to 100 words clearly
const sanitizeBrief = (brief: string): string => {
  return brief
    .split(/\s+/) // split by any whitespace
    .slice(0, 100) // take first 100 words
    .join(' ')
    .replace(/[^\x00-\x7F]/g, ''); // remove non-ASCII chars for safety
};

// 🧠 Builds full image prompt with creative and technical detail
const generateImagePrompt = (
  siteName: string,
  category: string,
  brief: string
): string => {
  const keyTerms = sanitizeBrief(brief);
  const basePrompt = `Create a photorealistic, professional image for "${siteName}", a ${category} website.
Style: High-quality commercial photography with vibrant colors and professional lighting.
Subject: ${getCategorySpecificSubject(category)}.
Environment: Clean, modern setting with depth of field.
Technical specifications: Ultra-detailed, 8k resolution, perfect composition, professional color grading.
Description: ${keyTerms}.
DO NOT include any text, UI elements, buttons, or web layouts in the image.`;

  const trimmedPrompt = basePrompt.length > 1000 ? basePrompt.slice(0, 1000) : basePrompt;

  console.log('[🔍 PROMPT]', trimmedPrompt);
  console.log('[🔢 LENGTH]', trimmedPrompt.length);

  return trimmedPrompt;
};

export async function POST(req: Request) {
  try {
    const { prompt, siteName, category, brief, n = 1, size } = await req.json();

    console.log('[📥 Request Received]');

    let finalPrompt = prompt;
    if (!finalPrompt && siteName && category && brief) {
      finalPrompt = generateImagePrompt(siteName, category, brief);
    }

    if (!finalPrompt) {
      console.error('[❌ Missing prompt]');
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    console.log('[🚀 Sending to OpenAI]', finalPrompt);

    const response = await openai.images.generate({
      prompt: finalPrompt,
      n: parseInt(n.toString()),
      size: size || '1024x1024',
    });

    const imageUrls = response.data.map(img => img.url).filter(Boolean);

    console.log('[✅ Generated Images]', imageUrls);

    if (imageUrls.length === 0) {
      return NextResponse.json({ error: 'Image generation failed' }, { status: 500 });
    }

    return NextResponse.json({ imageUrls });

  } catch (error: any) {
    console.error('[💥 OpenAI API Error]', error);
    return NextResponse.json(
      { error: error.message || 'Server error' },
      { status: 500 }
    );
  }
}


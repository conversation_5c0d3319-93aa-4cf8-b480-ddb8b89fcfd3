import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: Request) {
  const { brief } = await req.json();

  if (!brief) return NextResponse.json({ error: 'Brief is required' }, { status: 400 });

  try {
    const prompt = `Generate homepage content for a WordPress website based on the following brief:\n\n${brief}\n\nReturn a JSON with keys: heroHeadline, heroSubtext, about, services, contactCta.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    });

    const text = response.choices[0].message?.content || '{}';
    const json = JSON.parse(text);
    return NextResponse.json(json);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Content generation failed' }, { status: 500 });
  }
}

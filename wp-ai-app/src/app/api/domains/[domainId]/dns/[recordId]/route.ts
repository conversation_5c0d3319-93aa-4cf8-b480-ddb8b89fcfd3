import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

// PUT /api/domains/[domainId]/dns/[recordId] - Update a DNS record
export async function PUT(
  request: NextRequest,
  { params }: { params: { domainId: string; recordId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId, recordId } = params;

    if (!domainId || !recordId) {
      return NextResponse.json({ error: 'Domain ID and Record ID are required' }, { status: 400 });
    }

    // First, verify the user owns this domain
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    // Verify the DNS record exists and belongs to this domain
    const { data: existingRecord, error: recordError } = await supabaseAdmin
      .from('dns_records')
      .select('*')
      .eq('id', recordId)
      .eq('domain_id', domainId)
      .single();

    if (recordError || !existingRecord) {
      return NextResponse.json({ error: 'DNS record not found' }, { status: 404 });
    }

    const body = await request.json();
    const {
      type,
      name,
      value,
      ttl,
      priority,
      status
    } = body;

    // Validate required fields
    if (!type || !name || !value) {
      return NextResponse.json({ error: 'Type, name, and value are required' }, { status: 400 });
    }

    // Validate DNS record type
    const validTypes = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SRV', 'CAA'];
    if (!validTypes.includes(type)) {
      return NextResponse.json({ error: 'Invalid DNS record type' }, { status: 400 });
    }

    // Validate TTL
    if (ttl && (ttl < 60 || ttl > 604800)) {
      return NextResponse.json({ error: 'TTL must be between 60 and 604800 seconds' }, { status: 400 });
    }

    // Validate priority for MX records
    if (type === 'MX' && (priority === undefined || priority < 0 || priority > 65535)) {
      return NextResponse.json({ error: 'Priority is required for MX records and must be between 0 and 65535' }, { status: 400 });
    }

    // Update the DNS record
    const updateData: Partial<DNSRecord> = {
      type,
      name: name.trim(),
      value: value.trim(),
      updated_at: new Date().toISOString(),
    };

    if (ttl !== undefined) updateData.ttl = ttl;
    if (priority !== undefined) updateData.priority = type === 'MX' ? priority : null;
    if (status !== undefined) updateData.status = status;

    const { data: record, error } = await supabaseAdmin
      .from('dns_records')
      .update(updateData)
      .eq('id', recordId)
      .eq('domain_id', domainId)
      .select()
      .single();

    if (error) {
      console.error('Error updating DNS record:', error);
      return NextResponse.json({ error: 'Failed to update DNS record' }, { status: 500 });
    }

    return NextResponse.json({ record });
  } catch (error: any) {
    console.error('DNS record update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/domains/[domainId]/dns/[recordId] - Delete a DNS record
export async function DELETE(
  request: NextRequest,
  { params }: { params: { domainId: string; recordId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId, recordId } = params;

    if (!domainId || !recordId) {
      return NextResponse.json({ error: 'Domain ID and Record ID are required' }, { status: 400 });
    }

    // First, verify the user owns this domain
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    // Verify the DNS record exists and belongs to this domain
    const { data: existingRecord, error: recordError } = await supabaseAdmin
      .from('dns_records')
      .select('*')
      .eq('id', recordId)
      .eq('domain_id', domainId)
      .single();

    if (recordError || !existingRecord) {
      return NextResponse.json({ error: 'DNS record not found' }, { status: 404 });
    }

    // Delete the DNS record
    const { error } = await supabaseAdmin
      .from('dns_records')
      .delete()
      .eq('id', recordId)
      .eq('domain_id', domainId);

    if (error) {
      console.error('Error deleting DNS record:', error);
      return NextResponse.json({ error: 'Failed to delete DNS record' }, { status: 500 });
    }

    return NextResponse.json({ message: 'DNS record deleted successfully' });
  } catch (error: any) {
    console.error('DNS record deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
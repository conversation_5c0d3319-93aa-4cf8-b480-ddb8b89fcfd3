import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface Domain {
  id: string;
  domain_name: string;
  user_id: string;
  site_id: string | null;
  status: 'pending' | 'registered' | 'active' | 'expired' | 'failed';
  registration_date: string | null;
  expiry_date: string | null;
  auto_renew: boolean;
  price_paid: number | null;
  currency: string;
  namecheap_order_id: string | null;
  namecheap_transaction_id: string | null;
  stripe_session_id: string | null;
  dns_configured: boolean;
  cname_target: string | null;
  created_at: string;
  updated_at: string;
  site_name?: string; // From joined user-websites table
}

// GET /api/domains/[domainId] - Fetch a single domain by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { domainId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId } = params;

    if (!domainId) {
      return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
    }

    // Fetch the specific domain for the user
    const { data: domain, error } = await supabaseAdmin
      .from('domains')
      .select(`
        *,
        user-websites!site_id (
          site_name
        )
      `)
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error fetching domain:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Domain not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to fetch domain' }, { status: 500 });
    }

    // Transform the data to include site_name at the top level
    const transformedDomain = {
      ...domain,
      site_name: domain['user-websites']?.site_name || null,
      'user-websites': undefined // Remove the nested object
    };

    return NextResponse.json({ domain: transformedDomain });
  } catch (error: any) {
    console.error('Domain API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
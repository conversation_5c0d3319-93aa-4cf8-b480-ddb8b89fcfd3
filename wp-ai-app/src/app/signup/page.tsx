"use client";
import { useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { EyeOpenIcon, EyeClosedIcon } from "@/components/ui/EyeIcons";

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function SignupPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [accepted, setAccepted] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }
    if (!accepted) {
      setError("You must accept the terms and privacy policy");
      return;
    }
    const { error, data } = await supabase.auth.signUp({ email, password });
    if (error) setError(error.message);
    else window.location.href = "/dashboard";
  };

  return (
    <div style={{
      minHeight: "100vh",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      background: "linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%)",
      padding: 16,
    }}>
      <div style={{
        background: "white",
        padding: "48px 32px",
        borderRadius: "16px",
        boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.10)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%",
        maxWidth: 500,
        minWidth: 0,
        border: "1.5px solid #1DBF73",
      }}>
        <h2 style={{ marginBottom: 8, fontWeight: 700, fontSize: 26, color: "#1a3c34" }}>Welcome to WP AI</h2>
        <form style={{ width: "100%", marginTop: 16 }} onSubmit={handleSignup}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ fontWeight: 500, color: "#222", fontSize: 15 }}>Email address</label>
            <input
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
              style={{ width: "100%", padding: "10px 12px", border: "1px solid #e0e0e0", borderRadius: 8, marginTop: 6, fontSize: 15 }}
              placeholder="Enter your email"
            />
          </div>
          <div style={{ marginBottom: 16, position: "relative" }}>
            <label style={{ fontWeight: 500, color: "#222", fontSize: 15 }}>Password</label>
            <input
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              style={{ width: "100%", padding: "10px 12px", border: "1px solid #e0e0e0", borderRadius: 8, marginTop: 6, fontSize: 15 }}
              placeholder="Enter your password"
            />
            <span onClick={() => setShowPassword(v => !v)} style={{ position: "absolute", right: 12, top: 38, cursor: "pointer", color: "#888" }}>
              {showPassword ? <EyeClosedIcon /> : <EyeOpenIcon />}
            </span>
          </div>
          <div style={{ marginBottom: 16, position: "relative" }}>
            <label style={{ fontWeight: 500, color: "#222", fontSize: 15 }}>Confirm Password</label>
            <input
              type={showPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
              required
              style={{ width: "100%", padding: "10px 12px", border: "1px solid #e0e0e0", borderRadius: 8, marginTop: 6, fontSize: 15 }}
              placeholder="Confirm your password"
            />
            <span onClick={() => setShowPassword(v => !v)} style={{ position: "absolute", right: 12, top: 38, cursor: "pointer", color: "#888" }}>
              {showPassword ? <EyeClosedIcon /> : <EyeOpenIcon />}
            </span>
          </div>
          <div style={{ display: "flex", alignItems: "center", marginBottom: 16, fontSize: 14 }}>
            <input type="checkbox" checked={accepted} onChange={e => setAccepted(e.target.checked)} style={{ marginRight: 8 }} />
            I accept <a href="#" style={{ color: "#1DBF73", textDecoration: "underline", margin: "0 4px" }}>Terms of service</a> and <a href="#" style={{ color: "#1DBF73", textDecoration: "underline", margin: "0 4px" }}>Privacy policy</a>
          </div>
          {error && <div style={{ color: "#e53935", marginBottom: 8, fontSize: 14 }}>{error}</div>}
          {success && <div style={{ color: "#1DBF73", marginBottom: 8, fontSize: 14 }}>{success}</div>}
          <button type="submit" style={{ width: "100%", padding: "12px 0", background: "#1DBF73", color: "white", border: "none", borderRadius: 8, fontSize: 16, fontWeight: 600, marginBottom: 8, cursor: "pointer" }}>
            Sign Up
          </button>
        </form>
        <div style={{ marginTop: 8, color: "#444", fontSize: 15 }}>
          Already have an account? <a href="/login" style={{ color: "#1DBF73", fontWeight: 600, textDecoration: "none" }}>Sign in</a>
        </div>
      </div>
    </div>
  );
}

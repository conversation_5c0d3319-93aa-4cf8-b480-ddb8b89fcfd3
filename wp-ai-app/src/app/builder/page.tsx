//without loader
// 'use client';

// import { SiteBuilderProvider, useSiteBuilder } from '@/context/SiteBuilderContext';
// import { SiteBuilder } from '@/components/SiteBuilder';
// import { Layout } from '@/components/Layout';

// function BuilderLayoutWrapper() {
//   const { formData } = useSiteBuilder();
//   return (
//     <Layout currentStep={formData.step}>
//       <SiteBuilder />
//     </Layout>
//   );
// }

// export default function BuilderPage() {
//   return (
//     <SiteBuilderProvider>
//       <BuilderLayoutWrapper />
//     </SiteBuilderProvider>
//   );
// }


//with loader
// 'use client';

// import { SiteBuilderProvider, useSiteBuilder } from '@/context/SiteBuilderContext';
// import { SiteBuilder } from '@/components/SiteBuilder';
// import { Layout } from '@/components/Layout';
// import GlobalLoader from '@/components/GlobalLoader'; // ✅ Add this

// function BuilderLayoutWrapper() {
//   const { formData } = useSiteBuilder();
//   return (
//     <Layout currentStep={formData.step}>
//       <SiteBuilder />
//     </Layout>
//   );
// }

// export default function BuilderPage() {
//   return (
//     <SiteBuilderProvider>
//       <GlobalLoader /> {/* ✅ Use it here */}
//       <BuilderLayoutWrapper />
//     </SiteBuilderProvider>
//   );
// }



'use client';

import { SiteBuilderProvider, useSiteBuilder } from '@/context/SiteBuilderContext';
import { SiteBuilder } from '@/components/SiteBuilder';
import { Layout } from '@/components/Layout';
import GlobalLoader from '@/components/GlobalLoader';

function BuilderLayoutWrapper({ onExitToChoice }: { onExitToChoice: () => void }) {
  const { formData } = useSiteBuilder();
  return (
    <Layout currentStep={formData.step} onExitToChoice={onExitToChoice}>
      <SiteBuilder />
    </Layout>
  );
}

export default function BuilderPage({ onExitToChoice }: { onExitToChoice: () => void }) {
  return (
    <SiteBuilderProvider>
      <GlobalLoader />
      <BuilderLayoutWrapper onExitToChoice={onExitToChoice} />
    </SiteBuilderProvider>
  );
}

// utils/injectThemeStyles.ts
import { ThemeSettings } from 'src/components/customization/types';
import { FONT_OPTIONS } from 'src/components/customization/constants';

export function injectThemeStyles(
  iframe: HTMLIFrameElement,
  theme: ThemeSettings
) {
  if (!iframe.contentDocument) return;

  // 1. Inject Google Fonts
  const fontFamilies = [theme.headingFont, theme.bodyFont];
  let fontLink = iframe.contentDocument.getElementById('custom-font-link') as HTMLLinkElement | null;
  if (!fontLink) {
    fontLink = iframe.contentDocument.createElement('link');
    fontLink.id = 'custom-font-link';
    fontLink.rel = 'stylesheet';
    iframe.contentDocument.head.appendChild(fontLink);
  }
  const families = fontFamilies.map(f => f.replace(/ /g, '+')).join('|');
  fontLink.href = `https://fonts.googleapis.com/css?family=${families}:400,700&display=swap`;

  // 2. Inject style overrides
  let styleTag = iframe.contentDocument.getElementById('theme-style') as HTMLStyleElement | null;
  if (!styleTag) {
    styleTag = iframe.contentDocument.createElement('style');
    styleTag.id = 'theme-style';
    iframe.contentDocument.head.appendChild(styleTag);
  }
  styleTag.textContent = `
    body, p, li, span, div {
      font-family: '${theme.bodyFont}', ${FONT_OPTIONS.find(f => f.value === theme.bodyFont)?.category};
      color: ${theme.primaryColor} !important;
    }
    h1, h2, h3, h4, h5, h6 {
      font-family: '${theme.headingFont}', ${FONT_OPTIONS.find(f => f.value === theme.headingFont)?.category};
      color: ${theme.primaryColor} !important;
    }
    a, .btn, button {
      color: ${theme.primaryColor} !important;
    }
  `;
}

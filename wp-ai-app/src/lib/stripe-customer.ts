import { stripe } from './stripe';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface CustomerData {
  email: string;
  name?: string;
  phone?: string;
  address?: {
    line1?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  };
}

/**
 * Get or create a Stripe customer for a Supabase user
 */
export async function getOrCreateStripeCustomer(
  userId: string,
  userEmail: string
): Promise<string> {
  try {
    // First, check if user already has a Stripe customer ID in profiles table
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id, first_name, last_name, phone, address1, city, state_province, postal_code, country')
      .eq('email', userEmail)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching profile:', profileError);
      throw new Error('Failed to fetch user profile');
    }

    // If user already has a Stripe customer ID, verify it exists in Stripe
    if (profile?.stripe_customer_id) {
      try {
        const customer = await stripe.customers.retrieve(profile.stripe_customer_id);
        if (!customer.deleted) {
          return profile.stripe_customer_id;
        }
      } catch (error) {
        console.warn('Stripe customer not found, creating new one:', error);
      }
    }

    // Create new Stripe customer
    const customerData: CustomerData = {
      email: userEmail,
    };

    // Add name if available
    if (profile?.first_name || profile?.last_name) {
      customerData.name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim();
    }

    // Add phone if available
    if (profile?.phone) {
      customerData.phone = profile.phone;
    }

    // Add address if available
    if (profile?.address1 || profile?.city || profile?.state_province || profile?.postal_code || profile?.country) {
      customerData.address = {
        line1: profile.address1 || undefined,
        city: profile.city || undefined,
        state: profile.state_province || undefined,
        postal_code: profile.postal_code || undefined,
        country: profile.country || undefined,
      };
    }

    const customer = await stripe.customers.create(customerData);

    // Update the profiles table with the new Stripe customer ID
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({ stripe_customer_id: customer.id })
      .eq('email', userEmail);

    if (updateError) {
      console.error('Error updating profile with Stripe customer ID:', updateError);
      // Don't throw here as the customer was created successfully
    }

    return customer.id;
  } catch (error) {
    console.error('Error in getOrCreateStripeCustomer:', error);
    throw new Error('Failed to get or create Stripe customer');
  }
}

/**
 * Get Stripe customer ID from user profile
 */
export async function getStripeCustomerIdFromProfile(userEmail: string): Promise<string | null> {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id')
      .eq('email', userEmail)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }

    return profile?.stripe_customer_id || null;
  } catch (error) {
    console.error('Error in getStripeCustomerIdFromProfile:', error);
    return null;
  }
}

/**
 * Update Stripe customer with latest profile information
 */
export async function updateStripeCustomerFromProfile(
  stripeCustomerId: string,
  userEmail: string
): Promise<void> {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .select('first_name, last_name, phone, address1, city, state_province, postal_code, country')
      .eq('email', userEmail)
      .single();

    if (error) {
      console.error('Error fetching profile for update:', error);
      return;
    }

    const updateData: any = {};

    // Update name if available
    if (profile?.first_name || profile?.last_name) {
      updateData.name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim();
    }

    // Update phone if available
    if (profile?.phone) {
      updateData.phone = profile.phone;
    }

    // Update address if available
    if (profile?.address1 || profile?.city || profile?.state_province || profile?.postal_code || profile?.country) {
      updateData.address = {
        line1: profile.address1 || undefined,
        city: profile.city || undefined,
        state: profile.state_province || undefined,
        postal_code: profile.postal_code || undefined,
        country: profile.country || undefined,
      };
    }

    if (Object.keys(updateData).length > 0) {
      await stripe.customers.update(stripeCustomerId, updateData);
    }
  } catch (error) {
    console.error('Error updating Stripe customer:', error);
    // Don't throw as this is not critical
  }
}

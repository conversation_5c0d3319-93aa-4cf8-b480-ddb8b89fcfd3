# Australian Domain Registration Support

This document outlines the implementation of Australian domain registration support for `.com.au`, `.net.au`, and `.org.au` domains.

## Overview

Australian domain registration requires additional validation and information compared to other domain types. This implementation adds the necessary fields and validation to support Australian domain registration through Namecheap.

## Changes Made

### 1. Database Schema Updates

- Added `comau_registrant_id_type` (VARCHAR(10)) to profiles table
- Added `comau_registrant_id` (VARCHAR(50)) to profiles table
- Created indexes for better query performance

### 2. Profile Interface Updates

- Updated `Profile` interface in `AuthProvider.tsx` to include new fields
- Updated `fetchProfile` function to select new fields
- Updated `updateProfile` function to handle new fields

### 3. Profile Validation Modal Updates

- Added Australian domain registration section with:
  - Dropdown for Registrant ID Type (ABN, ACN, RBN, TM)
  - Text input for Registrant ID
- Added validation logic for Australian domains:
  - Required when country is Australia
  - Format validation for ABN (11 digits) and ACN (9 digits)
  - Basic validation for RBN and TM types

### 4. Account Profile Page Updates

- Added Australian domain registration section
- Integrated with existing profile editing functionality
- Maintains consistent UI/UX with existing fields

### 5. Domain Registration Validation

- Updated `validateProfileFields` function in domain complete page
- Added Australian domain detection (.com.au, .net.au, .org.au)
- Enhanced validation to require Australian fields for Australian domains
- Added format validation for different ID types

### 6. Namecheap API Integration

- Updated registrant construction to include Australian fields
- Maintains existing API structure while adding Australian-specific parameters
- Proper error handling for missing Australian fields

## Australian Registrant ID Types

1. **ABN (Australian Business Number)**: 11-digit number
2. **ACN (Australian Company Number)**: 9-digit number  
3. **RBN (Business Registration Number)**: Variable length
4. **TM (Trademark Number)**: Variable length

## Validation Rules

### For Australian Domains (.com.au, .net.au, .org.au):

1. **Required Fields**: Both `comau_registrant_id_type` and `comau_registrant_id` must be provided
2. **Format Validation**:
   - ABN: Exactly 11 digits
   - ACN: Exactly 9 digits
   - RBN/TM: At least 1 character
3. **Country Validation**: Automatically required when country is Australia

### For Non-Australian Domains:

- Australian fields are optional and not validated
- Registration proceeds normally without Australian-specific validation

## User Experience

### Profile Validation Modal

When registering an Australian domain, users will see:
1. Standard profile fields (name, address, etc.)
2. Australian domain registration section with:
   - Dropdown to select ID type
   - Text input for ID number
   - Clear instructions and validation messages

### Account Profile Page

Users can:
1. Edit Australian domain fields in their profile
2. Save changes with other profile information
3. View current Australian domain registration details

## API Integration

### Namecheap API Parameters

For Australian domains, the following parameters are added:
- `au_RegistrantIdType`: The selected ID type (ABN, ACN, RBN, TM)
- `au_RegistrantIdNumber`: The actual ID number

### Error Handling

- Missing Australian fields for Australian domains results in registration failure
- Format validation errors are displayed to users
- Clear error messages guide users to provide correct information

## Database Migration

Run the following SQL to add the new columns:

```sql
-- Add new columns for Australian domain registration
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS comau_registrant_id_type VARCHAR(10),
ADD COLUMN IF NOT EXISTS comau_registrant_id VARCHAR(50);

-- Add comments to document the new fields
COMMENT ON COLUMN profiles.comau_registrant_id_type IS 'Australian registrant ID type (ABN, ACN, RBN, TM) for .au domain registration';
COMMENT ON COLUMN profiles.comau_registrant_id IS 'Australian registrant ID number for .au domain registration';

-- Create an index for better query performance
CREATE INDEX IF NOT EXISTS idx_profiles_comau_registrant_id_type ON profiles(comau_registrant_id_type);
CREATE INDEX IF NOT EXISTS idx_profiles_comau_registrant_id ON profiles(comau_registrant_id);
```

## Testing

To test the implementation:

1. **Profile Updates**: Update profile with Australian domain fields
2. **Validation**: Try registering Australian domains with missing/invalid fields
3. **Registration**: Complete Australian domain registration with valid fields
4. **Non-Australian**: Verify non-Australian domains work without Australian fields

## Future Enhancements

1. **Enhanced Validation**: Add more sophisticated validation for different ID types
2. **Auto-detection**: Automatically detect Australian domains and show relevant fields
3. **Help Text**: Add more detailed help text for each ID type
4. **Format Masking**: Add input formatting for better user experience 
# Domain Search Ordering Test

This document outlines how to test the new domain search ordering functionality.

## Expected Order

When searching for domains, the results should now appear in this order:

1. **`.com.au`** - Australian commercial domains (highest priority)
2. **`.com`** - International commercial domains  
3. **`.ai`** - Artificial Intelligence domains
4. **All other domains** - .net, .org, .net.au, .org.au, .info, .biz, .co, .io, etc.

## How to Test

### 1. Search for a Domain

1. Go to the domain search page
2. Enter a domain name (e.g., "mybusiness")
3. Wait for the search results to load

### 2. Verify the Order

The results should show in this order:
- `mybusiness.com.au`
- `mybusiness.com` 
- `mybusiness.ai`
- `mybusiness.net`
- `mybusiness.org`
- `mybusiness.net.au`
- `mybusiness.org.au`
- `mybusiness.info`
- `mybusiness.biz`
- `mybusiness.co`
- `mybusiness.io`

### 3. Test with Different Search Terms

Try searching for:
- `test` → should show test.com.au, test.com, test.ai, etc.
- `example` → should show example.com.au, example.com, example.ai, etc.
- `business` → should show business.com.au, business.com, business.ai, etc.

## Implementation Details

### Changes Made

1. **Updated `COMMON_TLDS` array** in `src/app/api/namecheap/route.ts`:
   - Reordered to prioritize .com.au, .com, .ai
   - Added .ai domain support with pricing

2. **Updated `generateDomainVariations` function**:
   - Added .ai to the regex pattern for stripping domain extensions

3. **Added sorting logic** in `src/hooks/useDomainSearch.ts`:
   - `getDomainPriority()` function assigns priority scores
   - `sortDomainsByPriority()` function sorts results
   - Applied sorting in `performSearch()` function

### Priority Scoring

- `.com.au` domains: Priority 1 (highest)
- `.com` domains: Priority 2  
- `.ai` domains: Priority 3
- All other domains: Priority 4 (lowest)

## Notes

- The ordering is applied client-side after receiving results from the Namecheap API
- The original API response order is preserved within each priority group
- Available and unavailable domains maintain their status within the sorted order
- Premium domains are still highlighted appropriately 